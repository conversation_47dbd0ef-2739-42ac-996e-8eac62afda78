package com.example.springboot.controller;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.example.springboot.Utils.ExcelGenerator;
import com.example.springboot.common.Result;
import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.AiChatSession;
import com.example.springboot.entity.QuestionInfo;
import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.service.AIChatService;
import com.example.springboot.service.TokenService;
import com.example.springboot.tool.AdjustDataTools;
import com.example.springboot.tool.ReverseDataAdjustmentToolsAdvanced;
import com.example.springboot.mapper.AIChatSessionMapper;
import com.example.springboot.entity.FactorAnalysisOutput;
import com.example.springboot.entity.AnalysisResult;
import org.springframework.beans.factory.annotation.Qualifier;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.example.springboot.mapper.AIChatMessageMapper;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.time.LocalDateTime;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
@Slf4j
@RestController
@RequestMapping("/ai-chat")
public class AiChatController {

    @Autowired
    private AIChatService aiChatService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AdjustDataTools adjustDataTools;

    @Autowired
    private ReverseDataAdjustmentToolsAdvanced reverseDataAdjustmentTools;

    @Autowired
    private AIChatMessageMapper messageMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private AIChatSessionMapper sessionMapper;

    @PostMapping("/session")
    public ResponseEntity<AiChatSession> createSession(
            @RequestParam("tokenCode") String tokenCode,
            @RequestParam("surveyLink") String surveyLink,
            @RequestParam("excelFile") MultipartFile excelFile) {
        try {
            // 解析Excel数据
            List<List<String>> excelData = ExcelGenerator.parseExcelData(excelFile);
            
            // 解析问卷HTML获取题目列表
            String html = fetchSurveyHtml(surveyLink);
            List<QuestionInfo> questions = ExcelGenerator.parseSurveyQuestions(html);
            
            // 过滤和匹配Excel数据
            List<List<String>> filteredData = ExcelGenerator.filterAndMatchExcelData(excelData, questions);
            
            // 创建会话
            AiChatSession session = aiChatService.createSession(tokenCode, surveyLink, filteredData);
            return ResponseEntity.ok(session);
        } catch (Exception e) {
            log.error("创建会话失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    // 辅助方法：获取问卷HTML内容
    private String fetchSurveyHtml(String surveyLink) throws IOException {
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(surveyLink))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("请求被中断", e);
        }
    }

    @GetMapping("/session")
    public ResponseEntity<List<AiChatSession>> getSessions(@RequestParam("tokenCode") String tokenCode) {
        try {
            log.info("Getting sessions for token code: {}", tokenCode);
            List<AiChatSession> sessions = aiChatService.getSessions(tokenCode);
            log.info("Found {} sessions for token code: {}", sessions.size(), tokenCode);
            return ResponseEntity.ok(sessions);
        } catch (Exception e) {
            log.error("获取会话列表失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/session/{sessionId}")
    public ResponseEntity<AiChatSession> getSession(@PathVariable Long sessionId) {
        try {
            AiChatSession session = aiChatService.getSession(sessionId);
            return ResponseEntity.ok(session);
        } catch (Exception e) {
            log.error("获取会话详情失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/session/{sessionId}/messages")
    public ResponseEntity<List<AiChatMessage>> getMessages(@PathVariable Long sessionId) {
        try {
            List<AiChatMessage> messages = aiChatService.getMessages(sessionId);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            log.error("获取消息历史失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/session/{sessionId}/message")
    public ResponseEntity<Flux<AiChatMessage>> sendMessage(
            @PathVariable Long sessionId,
            @RequestBody String message) {
        try {
            AiChatSession session = aiChatService.getSession(sessionId);
            if (session == null) {
                return ResponseEntity.notFound().build();
            }
            // 优先用最新的complete_excel_data
            AiChatMessage latestMsg = aiChatService.findLatestAssistantMessageWithExcel(session.getUuid());
            String latestExcel = (latestMsg != null && latestMsg.getCompleteExcelData() != null && !latestMsg.getCompleteExcelData().isEmpty())
                ? latestMsg.getCompleteExcelData()
                : session.getExcelData();
            session.setExcelData(latestExcel);
            Flux<AiChatMessage> response = aiChatService.sendMessage(session.getId(), message);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/session/uuid/{uuid}/export")
    public ResponseEntity<byte[]> exportExcelByUuid(
            @PathVariable String uuid,
            @RequestBody List<List<String>> data) {
        try {
            byte[] excelData = aiChatService.exportExcelByUuid(uuid, data);
             return ResponseEntity.ok()
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                     // 文件名将在前端设置
                    .body(excelData);
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            return ResponseEntity.badRequest().body(e.getMessage().getBytes()); // 返回错误信息给前端
        }
    }

    @GetMapping("/session/uuid/{uuid}")
    public ResponseEntity<AiChatSession> getSessionByUuid(@PathVariable String uuid) {
        try {
            AiChatSession session = aiChatService.getSessionByUuid(uuid);
            if (session == null) {
                return ResponseEntity.notFound().build();
            }
            // 优先从消息表找最新的complete_excel_data
            String latestExcel = null;
            AiChatMessage latestMsg = aiChatService.findLatestAssistantMessageWithExcel(uuid);
            if (latestMsg != null && latestMsg.getCompleteExcelData() != null && !latestMsg.getCompleteExcelData().isEmpty()) {
                latestExcel = latestMsg.getCompleteExcelData();
            } else {
                latestExcel = session.getExcelData();
            }
            session.setExcelData(latestExcel);
            return ResponseEntity.ok(session);
        } catch (Exception e) {
            log.error("获取会话详情失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/session/uuid/{uuid}/messages")
    public ResponseEntity<List<AiChatMessage>> getMessagesByUuid(@PathVariable String uuid) {
        try {
            AiChatSession session = aiChatService.getSessionByUuid(uuid);
            if (session == null) {
                return ResponseEntity.notFound().build();
            }
            List<AiChatMessage> messages = aiChatService.getMessages(session.getId());
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            log.error("获取消息历史失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/session/uuid/{uuid}/message")
    public ResponseEntity<Flux<AiChatMessage>> sendMessageByUuid(
            @PathVariable String uuid,
            @RequestBody String message) {
        try {
            AiChatSession session = aiChatService.getSessionByUuid(uuid);
            if (session == null) {
                return ResponseEntity.notFound().build();
            }
            // 优先用最新的complete_excel_data
            AiChatMessage latestMsg = aiChatService.findLatestAssistantMessageWithExcel(uuid);
            String latestExcel = (latestMsg != null && latestMsg.getCompleteExcelData() != null && !latestMsg.getCompleteExcelData().isEmpty())
                ? latestMsg.getCompleteExcelData()
                : session.getExcelData();
            session.setExcelData(latestExcel);
            Flux<AiChatMessage> response = aiChatService.sendMessage(session.getId(), message);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/session/uuid/{uuid}/rename")
    public ResponseEntity<Void> renameSession(
            @PathVariable String uuid,
            @RequestParam String newName) {
        try {
            aiChatService.renameSession(uuid, newName);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("重命名会话失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/session/uuid/{uuid}")
    public ResponseEntity<Void> deleteSession(@PathVariable String uuid) {
        try {
            aiChatService.deleteSession(uuid);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除会话失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

  

    @GetMapping("/session/uuid/{sessionId}/modifications")
    public ResponseEntity<List<AiChatMessage.DataModification>> getSessionModifications(@PathVariable String sessionId) {
        return ResponseEntity.ok(aiChatService.getSessionDataModifications(sessionId));
    }

    @PostMapping("/session/uuid/{sessionId}/modifications")
    public ResponseEntity<Void> applyModification(
            @PathVariable String sessionId,
            @RequestBody AiChatMessage.DataModification modification) {
        aiChatService.applyDataModification(sessionId, modification);
        return ResponseEntity.ok().build();
    }


    @GetMapping("/session/uuid/{sessionId}/state")
    public ResponseEntity<Map<String, Object>> getSessionDataState(@PathVariable String sessionId) {
        return ResponseEntity.ok(aiChatService.getSessionDataState(sessionId));
    }


    @GetMapping("/session/{sessionId}/context")
    public Result<List<AiChatMessage>> getSessionContext(
            @PathVariable String sessionId,
            @RequestParam(defaultValue = "5") int limit) {
        try {
            List<AiChatMessage> context = aiChatService.getSessionContext(sessionId, limit);
            return Result.success(context);
        } catch (Exception e) {
            return Result.error("获取会话上下文失败: " + e.getMessage());
        }
    }

    @GetMapping("/session/{sessionId}/latest-excel")
    public Result<String> getLatestExcelData(@PathVariable String sessionId) {
        try {
            String excelData = aiChatService.getLatestExcelData(sessionId);
            return Result.success(excelData);
        } catch (Exception e) {
            return Result.error("获取最新Excel数据失败: " + e.getMessage());
        }
    }

    /**
     * 新增流式对话接口
     */
    @PostMapping(value = "/stream", produces = "text/event-stream;charset=UTF-8")
    public Flux<String> streamChat(@RequestParam String sessionId, @RequestParam String message) {
        AiChatSession session = aiChatService.getSessionByUuid(sessionId);
        if (session == null) {
            return Flux.just("data: {\"explanation\":\"会话不存在\"}\n\n");
        }
        // 优先用最新的complete_excel_data
        AiChatMessage latestMsg = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
        String latestExcel = (latestMsg != null && latestMsg.getCompleteExcelData() != null && !latestMsg.getCompleteExcelData().isEmpty())
            ? latestMsg.getCompleteExcelData()
            : session.getExcelData();
        session.setExcelData(latestExcel);

        StringBuilder completeResponse = new StringBuilder();
        return aiChatService.sendMessage(session.getId(), message)
                .map(msg -> {
                    if (msg.getContent() != null && !msg.getContent().trim().isEmpty()) {
                        Map<String, Object> result = new HashMap<>();
                        completeResponse.append(msg.getContent());
                        
                        // 尝试解析完整响应
                        try {
                            String completeJson = completeResponse.toString();
                            Map<String, Object> contentMap = objectMapper.readValue(completeJson, Map.class);
                            result.put("explanation", contentMap.get("explanation"));
                            // 只在消息完整时才包含 changedCells 和 tables
                            if (contentMap.containsKey("changedCells")) {
                                result.put("changedCells", contentMap.get("changedCells"));
                            }
                            if (contentMap.containsKey("tables")) {
                                result.put("tables", contentMap.get("tables"));
                            }
                            // 添加 tableData 字段
                            if (msg.getTableData() != null && !msg.getTableData().isEmpty()) {
                                result.put("tableData", msg.getTableData());
                            }
                        } catch (Exception e) {
                            // 如果解析失败，说明消息还不完整，只返回当前内容作为 explanation
                            result.put("explanation", msg.getContent());
                        }

                        String json;
                        try {
                            json = objectMapper.writeValueAsString(result);
                        } catch (Exception e) {
                            log.error("Error serializing message chunk for stream: {}", msg.getContent(), e);
                            json = "{\"explanation\":\"AI助手处理消息时发生错误\"}";
                        }
                        return "data: " + json + "\n\n";
                    } else {
                        return "";
                    }
                });
    }

    /**
     * 获取Excel数据版本历史
     */
    @GetMapping("/excel/versions")
    public ResponseEntity<?> getExcelVersions(@RequestParam String sessionId) {
        try {
            List<Map<String, Object>> versions = aiChatService.getExcelVersionHistory(sessionId);
            Map<String, Object> response = new HashMap<>();
            response.put("data", versions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Excel版本历史失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取版本历史失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定版本的Excel数据（含修改信息）
     */
    @GetMapping("/excel/version/{messageId}")
    public ResponseEntity<?> getExcelVersionData(
            @RequestParam String sessionId,
            @PathVariable String messageId) {
        try {
            Map<String, Object> result = aiChatService.getExcelVersionDataWithModifications(sessionId, messageId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取指定版本Excel数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取版本数据失败：" + e.getMessage());
        }
    }

    /**
     * 恢复指定版本的数据为当前版本
     */
    @PostMapping("/excel/version/{messageId}/restore")
    public ResponseEntity<?> restoreExcelVersion(@PathVariable("messageId") String messageId, @RequestParam("sessionId") String sessionId) {
        log.info("Received restore version request for session: {}, message: {}", sessionId, messageId);
        try {
            // 确保 sessionId 是有效的 UUID
            AiChatSession session = aiChatService.getSessionByUuid(sessionId);
            if (session == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("success", false, "message", "会话不存在"));
            }

            // 检查是否是初始版本
            if (messageId.startsWith("initial_")) {
                // 处理初始版本恢复
                aiChatService.restoreInitialExcelVersion(sessionId);
            } else {
                // 处理普通版本恢复
                try {
                    Long messageIdLong = Long.parseLong(messageId);
                    aiChatService.restoreExcelVersion(sessionId, messageIdLong);
                } catch (NumberFormatException e) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("success", false, "message", "无效的版本ID格式"));
                }
            }

            // 恢复成功后返回一个成功响应
            return ResponseEntity.ok(Map.of("success", true, "message", "版本恢复成功"));
        } catch (RuntimeException e) {
            log.error("恢复Excel版本失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("success", false, "message", "恢复版本失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("恢复Excel版本失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("success", false, "message", "恢复版本失败: 服务器内部错误"));
        }
    }

    @GetMapping("/session/uuid/{sessionId}/latest-assistant-message")
    public ResponseEntity<AiChatMessage> getLatestAssistantMessage(@PathVariable String sessionId) {
        try {
            AiChatMessage message = aiChatService.getLatestAssistantMessage(sessionId);
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            log.error("获取最新助手消息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/session/uuid/{sessionId}/survey-structure")
    public ResponseEntity<Map<String, Object>> getSurveyStructure(@PathVariable String sessionId) {
        try {
            WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
            if (surveyData == null || surveyData.getJsonData() == null) {
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("surveyData", surveyData.getJsonData());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取问卷结构失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 统一数据分析接口
     * @param uuid 会话uuid
     * @param params 前端传递的分析参数
     * @return 分析结果表格
     */
    @PostMapping("/session/uuid/{uuid}/analysis")
    public ResponseEntity<?> runAnalysis(@PathVariable String uuid, @RequestBody Map<String, Object> params) {
        try {
            String type = (String) params.get("type");
            List<Integer> columns = (List<Integer>) params.get("columns");
            List<Integer> colIndices = (List<Integer>) params.get("colIndices");
            Integer dependentVar = params.get("dependentVar") != null ? (Integer) params.get("dependentVar") : null;
            List<Integer> independentVars = (List<Integer>) params.get("independentVars");
            List<Integer> moderatorVars = (List<Integer>) params.get("moderatorVars");
            String centerType = (String) params.get("centerType");
            String variableType = (String) params.getOrDefault("variableType", "quantitative_quantitative");
            Integer groupCol = params.get("groupCol") != null ? (Integer) params.get("groupCol") : null;
            List<Integer> valueCols = (List<Integer>) params.get("valueCols");
            List<Integer> questionNums = (List<Integer>) params.get("questionNums");
            Double testValue = params.get("testValue") != null ? Double.valueOf(params.get("testValue").toString()) : null;
            String method = (String) params.get("method");
            Integer factors = params.get("factors") != null ? (Integer) params.get("factors") : null;
            List<Integer> groupCols = (List<Integer>) params.get("groupCols");

            List<FactorAnalysisOutput.TableData> result = null;
            switch (type) {
                case "cronbach": {
                    AnalysisResult.ReliabilityResult reliability = adjustDataTools.calculateCronbachAlpha(uuid, columns);
                    result = reliability.getTables();
                    break;
                }
                case "correlation":
                    result = adjustDataTools.correlationMatrixAnalysis(uuid, colIndices, method);
                    break;
                case "regression":
                    result = adjustDataTools.performRegression(uuid, dependentVar, independentVars);
                    break;
                case "anova":
                    result = adjustDataTools.performAnova(uuid, groupCol, valueCols);
                    break;
                case "independentTTest":
                    result = adjustDataTools.performIndependentTTest(uuid, groupCol, valueCols);
                    break;
                case "oneSampleTTest":
                    result = adjustDataTools.performOneSampleTTest(uuid, questionNums, testValue);
                    break;
                case "factor": {
                    FactorAnalysisOutput factor = adjustDataTools.performFactorAnalysis(uuid, columns, factors);
                    result = factor.getTables();
                    break;
                }
                case "moderation":
                    result = adjustDataTools.moderationEffectAnalysis(uuid, dependentVar, independentVars, moderatorVars, centerType, variableType);
                    break;
                case "mediation":
                    result = adjustDataTools.mediationEffectAnalysis(
                        uuid,
                        dependentVar,
                        independentVars,
                        params.get("mediatorVars") != null ? (List<Integer>) params.get("mediatorVars") : null,
                        (String) params.getOrDefault("mediationType", "parallel")
                    );
                    break;
                case "frequency":
                    result = adjustDataTools.frequencyStatistics(uuid, questionNums);
                    break;
                case "desc":
                    result = adjustDataTools.descriptiveStatistics(uuid, questionNums);
                    break;
                case "crossTab": {
                    // 兼容前端传参，questionCols和groupCols都为List<Integer>
                    List<Integer> questionCols = null;
                    Object qColsObj = params.get("questionCols");
                    if (qColsObj instanceof List<?>) {
                        questionCols = ((List<?>) qColsObj).stream().map(Object::toString).map(Integer::valueOf).toList();
                    }
                    List<Integer> groupColsParam = null;
                    Object gColsObj = params.get("groupCols");
                    if (gColsObj instanceof List<?>) {
                        groupColsParam = ((List<?>) gColsObj).stream().map(Object::toString).map(Integer::valueOf).toList();
                    }
                    if (questionCols == null || questionCols.isEmpty() || groupColsParam == null || groupColsParam.isEmpty()) {
                        return ResponseEntity.badRequest().body("交叉分析参数缺失，请检查X/Y变量选择");
                    }
                    result = adjustDataTools.crossTabChiSquareAnalysis(uuid, questionCols, groupColsParam);
                    break;
                 }
                default:
                    return ResponseEntity.badRequest().body("不支持的分析类型: " + type);
            }
            // 分析类型中文名
            Map<String, String> typeNameMap = new HashMap<>();
            typeNameMap.put("cronbach", "信度分析");
            typeNameMap.put("correlation", "相关分析");
            typeNameMap.put("regression", "回归分析");
            typeNameMap.put("anova", "方差分析");
            typeNameMap.put("independentTTest", "独立样本T检验");
            typeNameMap.put("oneSampleTTest", "单样本T检验");
            typeNameMap.put("factor", "因子分析");
            typeNameMap.put("moderation", "调节效应分析");
            typeNameMap.put("frequency", "频数统计");
            typeNameMap.put("desc", "描述性统计");
            typeNameMap.put("crossTab", "交叉分析");
            typeNameMap.put("mediation", "中介效应分析");

            AiChatMessage lastMsg = messageMapper.findLatestWaitingFillBySessionId(uuid);
            if (lastMsg != null) { 
                int maxOrder = messageMapper.getMaxMessageOrder(uuid);
                lastMsg.setRole("system");
                lastMsg.setContent(typeNameMap.getOrDefault(type, type));
                lastMsg.setMessageOrder(maxOrder + 1);
                messageMapper.update(lastMsg);
            }else{
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("没有找到等待填充的AI消息");
            }
          
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("分析失败: " + e.getMessage());
        }
    }

    /**
     * 分维度量表数据调整接口
     * @param params 调整参数
     * @return 调整结果
     */
    @PostMapping("/adjustment/multi-dimensional-scale")
    public ResponseEntity<String> adjustMultiDimensionalScale(@RequestBody Map<String, Object> params) {
        try {
            log.info("收到分维度量表调整请求: {}", params);

            // 提取参数
            String sessionId = (String) params.get("sessionId");

            // 处理dimensions参数
            @SuppressWarnings("unchecked")
            List<List<Object>> dimensionsRaw = (List<List<Object>>) params.get("dimensions");
            List<List<Integer>> dimensions = new ArrayList<>();
            for (List<Object> dim : dimensionsRaw) {
                List<Integer> intDim = new ArrayList<>();
                for (Object obj : dim) {
                    intDim.add(obj instanceof Integer ? (Integer) obj : Integer.valueOf(obj.toString()));
                }
                dimensions.add(intDim);
            }

            // 处理scaleLevel参数
            Integer scaleLevel = null;
            Object scaleLevelObj = params.get("scaleLevel");
            if (scaleLevelObj != null) {
                scaleLevel = scaleLevelObj instanceof Integer ? (Integer) scaleLevelObj : Integer.valueOf(scaleLevelObj.toString());
            }

            // 处理targetDimensionAlphas参数
            @SuppressWarnings("unchecked")
            List<Object> targetDimensionAlphasRaw = (List<Object>) params.get("targetDimensionAlphas");
            List<Double> targetDimensionAlphas = null;
            if (targetDimensionAlphasRaw != null) {
                targetDimensionAlphas = new ArrayList<>();
                for (Object obj : targetDimensionAlphasRaw) {
                    targetDimensionAlphas.add(obj instanceof Double ? (Double) obj : Double.valueOf(obj.toString()));
                }
            }

            Double targetTotalAlpha = params.get("targetTotalAlpha") != null ?
                Double.valueOf(params.get("targetTotalAlpha").toString()) : null;
            Double targetKMO = params.get("targetKMO") != null ?
                Double.valueOf(params.get("targetKMO").toString()) : null;
            Double targetInterDimensionCorrelation = params.get("targetInterDimensionCorrelation") != null ?
                Double.valueOf(params.get("targetInterDimensionCorrelation").toString()) : null;
            Double tolerance = params.get("tolerance") != null ?
                Double.valueOf(params.get("tolerance").toString()) : null;

            // 处理targetItemMeans参数
            @SuppressWarnings("unchecked")
            List<List<Object>> targetItemMeansRaw = (List<List<Object>>) params.get("targetItemMeans");
            List<List<Double>> targetItemMeans = null;
            if (targetItemMeansRaw != null) {
                targetItemMeans = new ArrayList<>();
                for (List<Object> dim : targetItemMeansRaw) {
                    List<Double> doubleDim = new ArrayList<>();
                    for (Object obj : dim) {
                        doubleDim.add(obj == null ? null :
                            (obj instanceof Double ? (Double) obj : Double.valueOf(obj.toString())));
                    }
                    targetItemMeans.add(doubleDim);
                }
            }

            @SuppressWarnings("unchecked")
            List<List<String>> scoringDirections = (List<List<String>>) params.get("scoringDirections");

            // 参数验证
            if (sessionId == null || sessionId.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("会话ID不能为空");
            }
            if (dimensions == null || dimensions.isEmpty()) {
                return ResponseEntity.badRequest().body("维度定义不能为空");
            }

            if (scaleLevel == null) {
                return ResponseEntity.badRequest().body("量表级数不能为空");
            }

            // 调用调整工具
            String result = reverseDataAdjustmentTools.adjustDataForMultiDimensionalScale(
                sessionId, dimensions, scaleLevel, targetDimensionAlphas, targetTotalAlpha,
                targetKMO, targetInterDimensionCorrelation, tolerance,
                targetItemMeans, scoringDirections
            );

            // 数据调整成功完成，扣除代币
            try {
                AiChatSession session = sessionMapper.findByUuid(sessionId);
                if (session != null) {
                    boolean tokenConsumed = tokenService.consumeToken(session.getTokenCode(), 1);
                    if (tokenConsumed) {
                        sessionMapper.updateTokenConsumed(session.getId(), 1);
                        log.info("[代币扣除] 分维度量表调整成功，扣除1个代币，会话ID={}", sessionId);
                    } else {
                        log.warn("[代币扣除] 代币扣除失败，会话ID={}", sessionId);
                    }
                } else {
                    log.warn("[代币扣除] 未找到会话信息，会话ID={}", sessionId);
                }
            } catch (Exception tokenError) {
                log.error("[代币扣除] 数据调整代币扣除异常，会话ID={}", sessionId, tokenError);
            }

            log.info("分维度量表调整完成，sessionId: {}", sessionId);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("分维度量表调整失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("调整失败: " + e.getMessage());
        }
    }

    /**
     * 添加消息接口
     */
    @PostMapping("/message")
    public ResponseEntity<String> addMessage(@RequestBody Map<String, Object> params) {
        try {
            String sessionId = (String) params.get("sessionId");
            String role = (String) params.get("role");
            String content = (String) params.get("content");

            if (sessionId == null || role == null || content == null) {
                return ResponseEntity.badRequest().body("参数不完整");
            }

            AiChatSession session = aiChatService.getSessionByUuid(sessionId);
            if (session == null) {
                return ResponseEntity.badRequest().body("会话不存在");
            }

            // 创建消息
            AiChatMessage message = new AiChatMessage();
            message.setSessionId(session.getUuid());
            message.setRole(role);
            message.setContent(content);
            message.setCreateTime(LocalDateTime.now());

            // 保存消息
            aiChatService.saveMessage(message);

            return ResponseEntity.ok("消息添加成功");

        } catch (Exception e) {
            log.error("添加消息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("添加消息失败: " + e.getMessage());
        }
    }

    /**
     * 更新"等待AI填充"的消息为系统消息
     */
    @PostMapping("/update-waiting-message")
    public ResponseEntity<String> updateWaitingMessageToSystem(@RequestBody Map<String, Object> params) {
        try {
            String sessionId = (String) params.get("sessionId");
            String adjustmentType = (String) params.get("adjustmentType");

            if (sessionId == null || adjustmentType == null) {
                return ResponseEntity.badRequest().body("参数不完整");
            }

            // 查找最新的"等待AI填充"消息
            AiChatMessage waitingMessage = messageMapper.findLatestWaitingFillBySessionId(sessionId);
            if (waitingMessage == null) {
                return ResponseEntity.badRequest().body("未找到等待填充的消息");
            }

            // 更新消息的role和content
            waitingMessage.setRole("system");
            waitingMessage.setContent(adjustmentType);

            // 保存更新
            messageMapper.update(waitingMessage);

            log.info("成功更新等待AI填充消息为系统消息，ID={}, 调整类型={}", waitingMessage.getId(), adjustmentType);

            return ResponseEntity.ok("消息更新成功");

        } catch (Exception e) {
            log.error("更新等待消息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("更新等待消息失败: " + e.getMessage());
        }
    }
}