// ai-chat.js，内容来自ai-chat.html的<script>标签
var currentSessionId = null;
let hot = null;
let currentTokenCode = null;
let allSessions = []; // 用于存储所有会话数据，方便搜索过滤
// 选中区域支持多个
let selectedRanges = [];
let addRangeBtn = null; // 悬浮按钮DOM

// 全局变量
let surveyStructure = null;
let isShowingTextAnswers = false; // 当前是否显示文本答案
let originalData = null; // 保存原始数据

// ========== 数据分析相关 ========== //
let surveyStructureInfo = null;

// 全局加载弹框函数
function showGlobalLoading(title = '加载中', subtitle = '请稍候...') {
    // 移除已存在的加载弹框
    hideGlobalLoading();

    const overlay = document.createElement('div');
    overlay.className = 'global-loading-overlay show';
    overlay.id = 'globalLoadingOverlay';

    overlay.innerHTML = `
        <div class="global-loading-container">
            <div class="global-loading-spinner"></div>
            <div class="global-loading-text">${title}</div>
            <div class="global-loading-subtext">${subtitle}</div>
        </div>
    `;

    document.body.appendChild(overlay);

    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

function hideGlobalLoading() {
    const overlay = document.getElementById('globalLoadingOverlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
            // 恢复背景滚动
            document.body.style.overflow = '';
        }, 300);
    }
}

// 按钮状态管理函数（保留用于其他地方可能需要）
function showButtonLoading(button, text = '加载中...') {
    if (!button) return;
    button.classList.add('btn-modern', 'loading');
    button.disabled = true;
    button.textContent = text;
}

function showButtonSuccess(button, text = '成功') {
    if (!button) return;
    button.classList.remove('loading', 'error');
    button.classList.add('btn-modern', 'success');
    button.disabled = false;
    button.textContent = text;
}

function showButtonError(button, text = '错误') {
    if (!button) return;
    button.classList.remove('loading', 'success');
    button.classList.add('btn-modern', 'error');
    button.disabled = false;
    button.textContent = text;

    // 3秒后恢复原状态
    setTimeout(() => {
        resetButtonState(button, button.getAttribute('data-original-text') || '确定');
    }, 3000);
}

function resetButtonState(button, originalText = '确定') {
    if (!button) return;
    button.classList.remove('loading', 'success', 'error');
    button.classList.add('btn-modern');
    button.disabled = false;
    button.textContent = originalText;
    button.setAttribute('data-original-text', originalText);
}

// 页面加载时拉取问卷结构
async function loadSurveyStructureForAnalysis() {
    if (!currentSessionId) {
        surveyStructureInfo = null;
        document.getElementById('analysisDynamicParams').innerHTML = '<div class="text-danger">请先选择会话</div>';
        document.getElementById('runAnalysisBtn').disabled = true;
        return;
    }
    document.getElementById('analysisDynamicParams').innerHTML = '<div class="text-info">加载中...</div>';
    document.getElementById('runAnalysisBtn').disabled = true;
    try {
        const res = await fetch(`/ai-chat/session/uuid/${currentSessionId}/survey-structure`);
        const data = await res.json();
        surveyStructureInfo = data.surveyData || data.structure || data;
        console.log('surveyStructureInfo:', surveyStructureInfo);
        if (!surveyStructureInfo || !Array.isArray(surveyStructureInfo) || surveyStructureInfo.length === 0) {
            document.getElementById('analysisDynamicParams').innerHTML = '<div class="text-danger">请先上传数据并选择会话，或刷新页面</div>';
            document.getElementById('runAnalysisBtn').disabled = true;
        } else {
            renderAnalysisParams();
            document.getElementById('runAnalysisBtn').disabled = false;
        }
    } catch (e) {
        surveyStructureInfo = null;
        document.getElementById('analysisDynamicParams').innerHTML = '<div class="text-danger">加载问卷结构失败，请重试</div>';
        document.getElementById('runAnalysisBtn').disabled = true;
        console.error('加载问卷结构失败', e);
    }
}

// 渲染分析参数区
function renderAnalysisParams() {
    const type = document.getElementById('analysisType').value;
    const paramDiv = document.getElementById('analysisDynamicParams');
    if (!surveyStructureInfo || !Array.isArray(surveyStructureInfo) || surveyStructureInfo.length === 0) {
        paramDiv.innerHTML = '<div class="text-danger">请先上传数据并选择会话，或刷新页面</div>';
        document.getElementById('runAnalysisBtn').disabled = true;
        return;
    }
    document.getElementById('runAnalysisBtn').disabled = false;

    // 需要穿梭框的分析类型
    const transferTypes = ["cronbach","factor","desc","frequency","correlation"];
    // 允许的题型（信度分析/量表分析等）
    const allowedTypes = ["5", "3", "4", "6single", "6multiple", "7", "8", "11"];
    // 题型中文
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};

    if (transferTypes.includes(type) || type === 'oneSampleTTest') {
        // 过滤可选题号
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: typeMap[q.type] || q.type
        }));
        let targetKeys = [];
        let extraHtml = '';
        if (type === 'factor') {
            extraHtml += `<div class="mb-3" style="margin-top:10px;max-width:220px;">
                <label>因子数量</label>
                <select class="form-select" id="analysisFactors">
                    <option value="">自动选择（默认）</option>
                    ${Array.from({length:10},(_,i)=>`<option value="${i+1}">${i+1} 个</option>`).join('')}
                </select>
                <div class='form-text'>可选，默认自动选择（特征根>1的个数）</div>
            </div>`;
        }
        if (type === 'oneSampleTTest') {
            extraHtml += `<div class="mb-3" style="margin-top:10px;max-width:220px;">
                <label>对比值 <span class='text-danger'>*</span></label>
                <input type="number" class="form-control" id="analysisTestValue" placeholder="请输入对比值">
                <div class='form-text'>不输入时默认对比值为0</div>
            </div>`;
        }
        paramDiv.innerHTML = `<div id="sortable-transfer-container"></div>${extraHtml}`;
        window.initTransferSortable('sortable-transfer-container', transferData, targetKeys, function(newTargetKeys) {
            window.transferSortableTargetKeys = newTargetKeys;
        });
        return;
    }

    // 交叉分析专用拖拽穿梭框
    if (type === 'crossTab') {
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: q.type
        }));
        let xKey = null; // X(定类) 只能一个
        let yKeys = [];  // Y(定类) 可多个
        paramDiv.innerHTML = `<div id="crossTab-transfer-container"></div>`;
        window.initCrossTabTransferSortable('crossTab-transfer-container', transferData, xKey, yKeys, function(newXKey, newYKeys) {
            window.crossTabXKey = newXKey;
            window.crossTabYKeys = newYKeys;
        });
        return;
    }

    // 其他分析类型
    let html = '';
    const questionOptions = (surveyStructureInfo || []).map(q => `<option value="${q.numId}">${q.numId} - ${q.title}</option>`).join('');
    const colOptions = (surveyStructureInfo || []).flatMap(q => (q.colIndices||[]).map(col => `<option value="${col}">${col} - ${q.title}</option>`)).join('');
    if (type === 'regression') {
        // 只允许可做回归的题型
        const allowedTypes = ["5", "3", "4", "6single", "6multiple", "7", "8", "11"];
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: q.type
        }));
        let yKey = null;
        let xKeys = [];
        paramDiv.innerHTML = `<div id="regression-transfer-container"></div>`;
        window.initRegressionTransferSortable('regression-transfer-container', transferData, yKey, xKeys, function(newYKey, newXKeys) {
            window.regressionYKey = newYKey;
            window.regressionXKeys = newXKeys;
        });
        return;
    } else if (type === 'anova') {
        // 方差分析拖拽穿梭框
        const allowedTypes = ["5", "3", "4", "6single", "6multiple", "7", "8", "11"];
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: q.type
        }));
        let xKey = null; // X(定类) 只能一个
        let yKeys = [];  // Y(定量) 可多个
        paramDiv.innerHTML = `<div id="anova-transfer-container"></div>`;
        window.initAnovaTransferSortable('anova-transfer-container', transferData, xKey, yKeys, function(newXKey, newYKeys) {
            window.anovaXKey = newXKey;
            window.anovaYKeys = newYKeys;
        });
        return;
    } else if (type === 'independentTTest') {
        // 独立T检验拖拽穿梭框
        const allowedTypes = ["5", "3", "4", "6single", "6multiple", "7", "8", "11"];
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: q.type
        }));
        let xKey = null; // X(定类,仅两组) 只能一个
        let yKeys = [];  // Y(定量) 可多个
        paramDiv.innerHTML = `<div id="independentTTest-transfer-container"></div>`;
        window.initIndependentTTestTransferSortable('independentTTest-transfer-container', transferData, xKey, yKeys, function(newXKey, newYKeys) {
            window.independentTTestXKey = newXKey;
            window.independentTTestYKeys = newYKeys;
        });
        return;
    } else if (type === 'oneSampleTTest') {
        html += `<div class="mb-3">
            <label>题号（多选） <span class='text-danger'>*</span></label>
            <select class="form-select" id="analysisQuestionNums" multiple required><option value="">请选择</option>${questionOptions}</select>
            <div class='form-text'>可多选，至少选择一个</div>
        </div>`;
        html += `<div class="mb-3">
            <label>对比值 <span class='text-danger'>*</span></label>
            <input type="number" class="form-control" id="analysisTestValue" placeholder="请输入对比值">
            <div class='form-text'>不输入时默认对比值为0</div>
        </div>`;
        paramDiv.innerHTML = html;
        return;
    } else if (type === 'moderation') {
        // 过滤可选题号
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: typeMap[q.type] || q.type
        }));
        let yKey = null;
        let xKey = null;
        let zKey = null;
        paramDiv.innerHTML = `
            <div class="mb-3" style="margin-top:10px;max-width:300px;">
                <label>变量类型组合 <span class='text-danger'>*</span></label>
                <select class="form-select" id="moderationVariableType">
                    <option value="quantitative_quantitative">X定量 Z定量（默认）</option>
                    <option value="quantitative_categorical">X定量 Z定类</option>
                    <option value="categorical_quantitative">X定类 Z定量</option>
                </select>
                <div class='form-text'>选择自变量X和调节变量Z的数据类型组合</div>
            </div>
            <div id="moderation-transfer-container"></div>`;
        window.initModerationTransferSortable('moderation-transfer-container', transferData, yKey, xKey, zKey, function(newYKey, newXKey, newZKey) {
            window.moderationYKey = newYKey;
            window.moderationXKey = newXKey;
            window.moderationZKey = newZKey;
        });
        return;
    } else if (type === 'mediation') {
        // 过滤可选题号
        const candidates = surveyStructureInfo.filter(q => allowedTypes.includes(q.type));
        candidates.sort((a, b) => Number(a.numId) - Number(b.numId));
        const transferData = candidates.map(q => ({
            key: String(q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId),
            numId: Number(q.numId),
            title: `${q.numId}、${q.title}`,
            type: typeMap[q.type] || q.type
        }));
        let yKey = null;
        let xKeys = [];
        let mKeys = [];
        let mediationType = 'parallel';
        paramDiv.innerHTML = `<div id="mediation-transfer-container"></div>
        <div class='mb-3' style='margin-top:10px;max-width:220px;'>
            <label>中介类型</label>
            <select class='form-select' id='mediationTypeSelect'>
                <option value='parallel'>平行中介</option>
                <option value='chain'>链式中介</option>
            </select>
        </div>`;
        window.initMediationTransferSortable('mediation-transfer-container', transferData, yKey, xKeys, mKeys, function(newYKey, newXKeys, newMKeys) {
            window.mediationYKey = newYKey;
            window.mediationXKeys = newXKeys;
            window.mediationMKeys = newMKeys;
        });
        document.getElementById('mediationTypeSelect').addEventListener('change', function() {
            window.mediationType = this.value;
        });
        return;
    }
    paramDiv.innerHTML = html;
}

// 渲染穿梭框列表
function renderTransferLists() {
    // 初始化时保存原始顺序
    if (!window.allTransferItems) {
        window.allTransferItems = [...window.transferLeft, ...window.transferRight];
    }
    // 左右两侧的value集合
    const leftValues = new Set(window.transferLeft.map(i => i.value));
    const rightValues = new Set(window.transferRight.map(i => i.value));
    // 按原始顺序渲染
    const leftList = document.getElementById('transferLeftList');
    const rightList = document.getElementById('transferRightList');
    leftList.innerHTML = window.allTransferItems.filter(i => leftValues.has(i.value)).map(item => `<li class="list-group-item" data-value="${item.value}" data-type="${item.type}">${item.label}<span class="badge bg-light text-dark ms-2">${item.typeInfo}</span></li>`).join('');
    rightList.innerHTML = window.allTransferItems.filter(i => rightValues.has(i.value)).map(item => `<li class="list-group-item" data-value="${item.value}" data-type="${item.type}">${item.label}<span class="badge bg-primary ms-2">${item.typeInfo}</span></li>`).join('');
    enableTransferMultiSelectAndDrag();
}

// 穿梭框移动
function moveTransferItems(from, to) {
    const fromList = from==='left'?window.transferLeft:window.transferRight;
    const toList = to==='right'?window.transferRight:window.transferLeft;
    const fromUl = document.getElementById(from==='left'?'transferLeftList':'transferRightList');
    const toUl = document.getElementById(from==='left'?'transferRightList':'transferLeftList');
    // 获取所有选中的li索引
    const selectedLis = Array.from(fromUl.querySelectorAll('.selected'));
    if(selectedLis.length===0) return;
    // 记录索引并按原顺序排序
    const indices = selectedLis.map(li => Array.from(fromUl.children).indexOf(li)).sort((a,b)=>a-b);
    // 批量移动，保持原顺序
    const moving = indices.map(idx => fromList[idx]);
    // 计算插入点：目标区当前选中后面，否则末尾
    let insertIdx = toUl && toUl.querySelector('.selected') ? Array.from(toUl.children).indexOf(toUl.querySelector('.selected'))+1 : toList.length;
    toList.splice(insertIdx, 0, ...moving);
    // 从大到小删除，防止错位
    indices.sort((a,b)=>b-a).forEach(idx => fromList.splice(idx,1));
    renderTransferLists();
    enableTransferMultiSelectAndDrag();
}

// 收集参数并请求分析
async function runAnalysis() {
    if (!currentSessionId) { alert('请先选择会话'); return; }
    const analysisTypeEl = document.getElementById('analysisType');
    if (!analysisTypeEl) {
        alert('未找到分析类型下拉框，请刷新页面重试');
        return;
    }
    const type = analysisTypeEl.value;
    let params = { type };
    // 参数校验
    function getSelectedValues(id) {
        const el = document.getElementById(id);
        if (!el) return [];
        return Array.from(el.selectedOptions).map(o=>o.value).filter(v=>v);
    }
    function isEmpty(val) {
        return val === undefined || val === null || val === '' || (Array.isArray(val) && val.length === 0);
    }
    // 穿梭框分析类型收集右侧
    const transferTypes = ["cronbach","factor","desc","frequency","correlation"];
    if (transferTypes.includes(type)) {
        const selected = (window.transferSortableTargetKeys || []);
        if(selected.length===0) { alert('请选择分析项'); return; }
        if (type === 'correlation') {
            params.colIndices = selected.map(Number);
            params.method = document.getElementById('analysisMethod').value;
         } else if (type === 'frequency') {
            params.questionNums = selected.map(Number);
        } else if (type === 'desc') {
            params.questionNums = selected.map(Number);
        }else {
            params.columns = selected.map(Number);
            if (type === 'factor') {
                const factors = document.getElementById('analysisFactors')?.value;
                if (factors && factors !== '') params.factors = parseInt(factors);
            }
        }
    } else if (type === 'crossTab') {
        const xKey = window.crossTabXKey;
        const yKeys = window.crossTabYKeys || [];
        if (!xKey) { alert('请拖入一个分组变量（X）'); return; }
        if (yKeys.length === 0) { alert('请拖入至少一个被解释变量（Y）'); return; }
        params.groupCols = [Number(xKey)];
        params.questionCols = yKeys.map(Number);
    } else {
        // 按类型收集参数并校验
        if (type === 'regression') {
            const yKey = window.regressionYKey;
            const xKeys = window.regressionXKeys || [];
            if (!yKey) { alert('请拖入一个因变量（Y）'); return; }
            if (xKeys.length === 0) { alert('请拖入至少一个自变量（X）'); return; }
            params.dependentVar = Number(yKey);
            params.independentVars = xKeys.map(Number);
        } else if (type === 'anova') {
            const xKey = window.anovaXKey;
            const yKeys = window.anovaYKeys || [];
            if (!xKey) { alert('请拖入一个分组变量（X）'); return; }
            if (yKeys.length === 0) { alert('请拖入至少一个被解释变量（Y）'); return; }
            params.groupCol = Number(xKey);
            params.valueCols = yKeys.map(Number);
        } else if (type === 'independentTTest') {
            const xKey = window.independentTTestXKey;
            const yKeys = window.independentTTestYKeys || [];
            if (!xKey) { alert('请拖入一个分组变量（X）'); return; }
            if (yKeys.length === 0) { alert('请拖入至少一个被解释变量（Y）'); return; }
            params.groupCol = Number(xKey);
            params.valueCols = yKeys.map(Number);
        } else if (type === 'oneSampleTTest') {
            const selected = (window.transferSortableTargetKeys || []);
            let testValue = document.getElementById('analysisTestValue').value;
            if(selected.length===0) { alert('请选择分析项'); return; }
            if (isEmpty(testValue)) testValue = 0; // 默认0
            params.questionNums = selected.map(Number);
            params.testValue = parseFloat(testValue);
        } else if (type === 'moderation') {
            const yKey = window.moderationYKey;
            const xKey = window.moderationXKey;
            const zKey = window.moderationZKey;
            const variableType = document.getElementById('moderationVariableType')?.value || 'quantitative_quantitative';
            if (!yKey) { alert('请拖入一个因变量Y'); return; }
            if (!xKey) { alert('请拖入一个自变量X'); return; }
            if (!zKey) { alert('请拖入一个调节变量Z'); return; }
            params.dependentVar = Number(yKey);
            params.independentVars = [Number(xKey)];
            params.moderatorVars = [Number(zKey)];
            params.centerType = 'center'; // 默认中心化
            params.variableType = variableType; // 变量类型组合
        } else if (type === 'mediation') {
            const yKey = window.mediationYKey;
            const xKeys = window.mediationXKeys || [];
            const mKeys = window.mediationMKeys || [];
            const mediationType = document.getElementById('mediationTypeSelect')?.value || 'parallel';
            if (!yKey) { alert('请拖入一个因变量Y'); return; }
            if (xKeys.length === 0) { alert('请拖入至少一个自变量X'); return; }
            if (mKeys.length === 0) { alert('请拖入至少一个中介变量M'); return; }
            params.dependentVar = Number(yKey);
            params.independentVars = xKeys.map(Number);
            params.mediatorVars = mKeys.map(Number);
            params.mediationType = mediationType;
        }
    }
    // 发起请求
     try {
        const res = await fetch(`/ai-chat/session/uuid/${currentSessionId}/analysis`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(params)
        });
        if (!res.ok) {
            return;
        }
        // 关闭参数配置弹窗
        const paramModal = bootstrap.Modal.getInstance(document.getElementById('analysisParamModal'));
        if(paramModal) paramModal.hide();
        // 刷新AI聊天区
        if(currentSessionId) {
            loadMessages(currentSessionId);
            // 延迟刷新会话列表，确保后端操作完成
            setTimeout(() => {
                loadSessions();
            }, 1000);
        }
    } catch (error) {
        console.error('分析请求失败:', error);
        alert('分析请求失败: ' + error.message);
    }
}

// ========== 数据调整相关 ========== //
let surveyStructureInfoForAdjustment = null;

// 页面加载时拉取问卷结构（用于数据调整）
async function loadSurveyStructureForAdjustment() {
    if (!currentSessionId) {
        surveyStructureInfoForAdjustment = null;
        document.getElementById('adjustmentDynamicParams').innerHTML = '<div class="text-danger">请先选择会话</div>';
        document.getElementById('runAdjustmentBtn').disabled = true;
        return;
    }
    document.getElementById('adjustmentDynamicParams').innerHTML = '<div class="text-info">加载中...</div>';
    document.getElementById('runAdjustmentBtn').disabled = true;
    try {
        const res = await fetch(`/ai-chat/session/uuid/${currentSessionId}/survey-structure`);
        const data = await res.json();
        surveyStructureInfoForAdjustment = data.surveyData || data.structure || data;
        console.log('surveyStructureInfoForAdjustment:', surveyStructureInfoForAdjustment);
        if (!surveyStructureInfoForAdjustment || !Array.isArray(surveyStructureInfoForAdjustment) || surveyStructureInfoForAdjustment.length === 0) {
            document.getElementById('adjustmentDynamicParams').innerHTML = '<div class="text-danger">请先上传数据并选择会话，或刷新页面</div>';
            document.getElementById('runAdjustmentBtn').disabled = true;
        } else {
            renderAdjustmentParams();
            document.getElementById('runAdjustmentBtn').disabled = false;
        }
    } catch (e) {
        surveyStructureInfoForAdjustment = null;
        document.getElementById('adjustmentDynamicParams').innerHTML = '<div class="text-danger">加载问卷结构失败，请重试</div>';
        document.getElementById('runAdjustmentBtn').disabled = true;
        console.error('加载问卷结构失败', e);
    }
}

// 渲染调整参数区
function renderAdjustmentParams() {
    const type = document.getElementById('adjustmentType').value;
    const paramDiv = document.getElementById('adjustmentDynamicParams');
    if (!surveyStructureInfoForAdjustment || !Array.isArray(surveyStructureInfoForAdjustment) || surveyStructureInfoForAdjustment.length === 0) {
        paramDiv.innerHTML = '<div class="text-danger">请先上传数据并选择会话，或刷新页面</div>';
        document.getElementById('runAdjustmentBtn').disabled = true;
        return;
    }
    document.getElementById('runAdjustmentBtn').disabled = false;

    if (type === 'multiDimensionalScale') {
        renderMultiDimensionalScaleParams(paramDiv);
    }
}

// 渲染分维度调整量表参数
function renderMultiDimensionalScaleParams(paramDiv) {
    // 分维度调整量表只支持特定题型：3(单选)、5(量表)、6single(矩阵单选)
    const allowedTypes = ["3", "5", "6single"];
    const filteredQuestions = surveyStructureInfoForAdjustment.filter(q => allowedTypes.includes(q.type));

    if (filteredQuestions.length === 0) {
        paramDiv.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> 没有找到适合分维度量表调整的题目</h6>
                <p class="mb-0">分维度量表调整只支持以下题型：</p>
                <ul class="mb-0">
                    <li><strong>单选题</strong> (类型3)</li>
                    <li><strong>单项量表题</strong> (类型5)</li>
                    <li><strong>矩阵单选题</strong> (类型6single)</li>
                </ul>
            </div>
        `;
        document.getElementById('runAdjustmentBtn').disabled = true;
        return;
    }



    // 计算实际可用题目数量（包括矩阵题中的小题）
    const totalAvailableQuestions = calculateTotalAvailableQuestions(filteredQuestions);

    let html = `
        <div class="alert alert-info mb-3">
            <div class="row">
                <div class="col-md-8">
                    <h6><i class="bi bi-info-circle"></i> 分维度量表调整说明</h6>
                    <p class="mb-0">此功能可以调整多维度量表的信度和效度，确保每个维度都能通过统计检验。只支持单选题、单项量表题、矩阵单选题。</p>
                </div>
                <div class="col-md-4">
                    <strong>可用题目：</strong>
                    <span class="badge bg-primary">${totalAvailableQuestions}题</span>
                    <div id="currentScaleInfo" class="mt-2" style="display: none;">
                        <strong>当前量表级数：</strong>
                        <span id="currentScaleLevel" class="badge bg-success"></span>
                    </div>
                </div>
            </div>
        </div>
        <div style="display:flex;flex-direction:row;gap:20px;height:100%;">
            <!-- 左侧：维度定义区域 -->
            <div style="flex:1;min-height:0;display:flex;flex-direction:column;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">维度定义 <span class="text-danger">*</span></h6>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addDimension()">
                        <i class="bi bi-plus"></i> 添加维度
                    </button>
                </div>
                <div id="dimensionsContainer" style="flex:1;border:1px solid #ddd;border-radius:8px;overflow:hidden;display:flex;flex-direction:column;">
                    <div id="dimensionsList" style="flex:1;overflow-y:auto;padding:15px;"></div>
                </div>
            </div>

            <!-- 右侧：参数设置区域 -->
            <div style="flex:0 0 380px;display:flex;flex-direction:column;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">全局参数设置</h6>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportConfig()" title="导出当前所有配置到剪贴板">
                            <i class="bi bi-download"></i> 导出
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="importConfig()" title="从剪贴板导入配置">
                            <i class="bi bi-upload"></i> 导入
                        </button>
                    </div>
                </div>
                <div class="card" style="flex:1;">
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">总量表目标信度</label>
                            <input type="number" class="form-control" id="targetTotalAlpha" step="0.01" min="0.5" max="1" value="0.85" placeholder="默认0.85" onchange="validateInput(this, 0.5, 1, '总信度系数')">
                            <small class="form-text text-muted">建议范围：0.8以上</small>
                            <div class="invalid-feedback" id="targetTotalAlpha-error"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">目标KMO值</label>
                            <input type="number" class="form-control" id="targetKMO" step="0.01" min="0.5" max="1" value="0.8" placeholder="默认0.7" onchange="validateInput(this, 0.5, 1, 'KMO值')">
                            <small class="form-text text-muted">建议范围：0.6以上（0.8以上为优秀）</small>
                            <div class="invalid-feedback" id="targetKMO-error"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">维度间目标相关系数</label>
                            <input type="number" class="form-control" id="targetInterDimensionCorrelation" step="0.01" min="0" max="1" value="0.4" placeholder="默认0.4" onchange="validateInput(this, 0, 1, '维度间相关系数')">
                            <small class="form-text text-muted">建议范围：0.3-0.7</small>
                            <div class="invalid-feedback" id="targetInterDimensionCorrelation-error"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">允许误差范围</label>
                            <input type="number" class="form-control" id="tolerance" step="0.001" min="0.001" max="0.5" value="0.02" placeholder="默认0.02" onchange="validateInput(this, 0.001, 0.5, '容差')">
                            <small class="form-text text-muted">数值越小要求越严格</small>
                            <div class="invalid-feedback" id="tolerance-error"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    paramDiv.innerHTML = html;

    // 初始化全局变量来跟踪已使用的题目
    window.usedQuestions = new Set();

    // 初始化一个维度
    addDimension();
}

// 检查量表级数一致性
function checkScaleConsistency(questions) {
    const scaleLevels = new Map();
    const details = [];

    questions.forEach(q => {
        let scaleLevel = 5; // 默认5级

        // 根据题目类型和选项数量确定量表级数
        if (q.type === '3' || q.type === '5') {
            // 单选题和量表题，根据选项数量确定
            scaleLevel = q.options ? q.options.length : 5;
        } else if (q.type === '6single') {
            // 矩阵单选题，根据列数确定
            scaleLevel = q.cols || 5;
        }

        const key = `${scaleLevel}级`;
        if (!scaleLevels.has(key)) {
            scaleLevels.set(key, []);
        }
        scaleLevels.get(key).push(`Q${q.numId}`);
    });

    // 检查是否一致
    const uniqueScales = Array.from(scaleLevels.keys());
    const isConsistent = uniqueScales.length === 1;

    if (!isConsistent) {
        uniqueScales.forEach(scale => {
            const questionList = scaleLevels.get(scale);
            details.push(`${scale}量表: ${questionList.join(', ')}`);
        });
    }

    return {
        isConsistent,
        details,
        mainScaleLevel: uniqueScales[0] || '5级'
    };
}

// 获取量表信息
function getScaleInfo(questions) {
    if (questions.length === 0) {
        return {
            scaleLevel: 5,
            suggestedDimensions: 0
        };
    }

    // 获取主要的量表级数
    const scaleLevels = questions.map(q => {
        if (q.type === '3' || q.type === '5') {
            return q.options ? q.options.length : 5;
        } else if (q.type === '6single') {
            // 对于矩阵题，检查子题的选项数量
            if (q.subQuestions && q.subQuestions.length > 0) {
                const firstSubQ = q.subQuestions[0];
                if (firstSubQ && firstSubQ.options && firstSubQ.options.length > 0) {
                    return firstSubQ.options.length;
                }
            }
            // 如果没有子题信息，尝试使用 cols 字段
            return q.cols || 5;
        }
        return 5;
    });

    // 取最常见的量表级数
    const scaleCount = {};
    scaleLevels.forEach(level => {
        scaleCount[level] = (scaleCount[level] || 0) + 1;
    });

    const mainScaleLevel = Object.keys(scaleCount).reduce((a, b) =>
        scaleCount[a] > scaleCount[b] ? a : b
    );

    // 建议维度数：题目数量的1/3到1/2，最少2个，最多8个
    const suggestedDimensions = Math.max(2, Math.min(8, Math.ceil(questions.length / 4)));

    return {
        scaleLevel: parseInt(mainScaleLevel),
        suggestedDimensions
    };
}

// 计算实际可用题目数量（包括矩阵题中的小题）
function calculateTotalAvailableQuestions(questions) {
    let total = 0;

    questions.forEach(q => {
        if (q.type === '6single' && q.subQuestions && q.subQuestions.length > 0) {
            // 矩阵题：计算小题数量
            total += q.subQuestions.length;
        } else {
            // 普通题目：计算为1题
            total += 1;
        }
    });

    return total;
}

// 获取题目的量表级数
function getQuestionScaleLevel(question) {
    if (question.type === '3' || question.type === '5') {
        return question.options ? question.options.length : 5;
    } else if (question.type === '6single') {
        // 对于矩阵题，检查子题的选项数量
        if (question.subQuestions && question.subQuestions.length > 0) {
            const firstSubQ = question.subQuestions[0];
            if (firstSubQ && firstSubQ.options && firstSubQ.options.length > 0) {
                return firstSubQ.options.length;
            }
        }
        // 如果没有子题信息，尝试使用 cols 字段
        return question.cols || 5;
    }
    return 5;
}

// 获取当前已选题目的量表级数
function getCurrentScaleLevel() {
    // 遍历所有已选题目，获取第一个题目的量表级数
    for (const questionId of window.usedQuestions) {
        let question;

        if (typeof questionId === 'string' && questionId.includes('_')) {
            // 矩阵小题
            const mainQuestionId = parseInt(questionId.split('_')[0]);
            question = surveyStructureInfoForAdjustment.find(q => q.numId === mainQuestionId);
        } else {
            // 普通题目
            question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
        }

        if (question) {
            return getQuestionScaleLevel(question);
        }
    }

    return null; // 没有已选题目
}

// 更新量表级数显示
function updateScaleInfoDisplay() {
    const scaleInfoDiv = document.getElementById('currentScaleInfo');
    const scaleLevelSpan = document.getElementById('currentScaleLevel');

    if (!scaleInfoDiv || !scaleLevelSpan) return;

    const currentScaleLevel = getCurrentScaleLevel();

    if (currentScaleLevel !== null) {
        scaleLevelSpan.textContent = `${currentScaleLevel}级`;
        scaleInfoDiv.style.display = 'block';
    } else {
        scaleInfoDiv.style.display = 'none';
    }
}

// 添加维度
function addDimension() {
    const dimensionsList = document.getElementById('dimensionsList');
    const dimensionIndex = dimensionsList.children.length;

    // 分维度调整量表只支持特定题型
    const allowedTypes = ["3", "5", "6single"];
    const filteredQuestions = surveyStructureInfoForAdjustment.filter(q => allowedTypes.includes(q.type));

    // 过滤掉已使用的题目，对于矩阵题目，只要还有可用的小题就显示
    const availableQuestions = filteredQuestions.filter(q => {
        // 检查普通题目是否被使用
        const questionUsed = window.usedQuestions.has(q.numId);

        // 如果是矩阵题目，检查是否还有可用的小题
        if (q.subQuestions && q.subQuestions.length > 0) {
            // 检查是否还有未被使用的小题
            let hasAvailableSubQuestions = false;
            for (let i = 0; i < q.subQuestions.length; i++) {
                if (!window.usedQuestions.has(`${q.numId}_${i}`)) {
                    hasAvailableSubQuestions = true;
                    break;
                }
            }
            return hasAvailableSubQuestions;
        } else {
            // 普通题目：只要没被使用就显示
            return !questionUsed;
        }
    });

    if (availableQuestions.length === 0) {
        alert('没有更多可用的题目了，所有题目都已被分配到其他维度');
        return;
    }

    const dimensionHtml = `
        <div class="dimension-item mb-3" data-dimension-index="${dimensionIndex}">
            <div class="card border-primary">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-primary">
                        <i class="bi bi-diagram-3"></i> 维度 ${dimensionIndex + 1}
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeDimension(${dimensionIndex})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <!-- 题目选择区域 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">选择题目 <span class="text-danger">*</span></label>
                        <div class="border rounded p-2 question-selector-container">
                            <div id="questionSelector_${dimensionIndex}" class="question-selector">
                                ${availableQuestions.map(q => {
                                    // 检查是否为矩阵量表题
                                    if ((q.type === '6single' || q.type === '3' || q.type === '5') && q.subQuestions && q.subQuestions.length > 0) {
                                        // 矩阵量表题，显示可展开的小题
                                        return `
                                            <div class="form-check mb-2">
                                                <div class="d-flex align-items-center">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary me-2"
                                                            onclick="toggleMatrixSubQuestions(${dimensionIndex}, ${q.numId})"
                                                            id="toggleBtn_${dimensionIndex}_${q.numId}">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                    <strong>Q${q.numId}</strong> - ${q.title}
                                                    <span class="badge bg-secondary ms-2">${getQuestionTypeDescription(q.type)}</span>
                                                    <span class="badge bg-info ms-2">${q.subQuestions.length}个小题</span>
                                                </div>
                                                <div id="subQuestions_${dimensionIndex}_${q.numId}" class="ms-4 mt-2" style="display:none;">
                                                    ${q.subQuestions.map((subQ, subIdx) => {
                                                        const subQuestionId = `${q.numId}_${subIdx}`;
                                                        const isUsed = window.usedQuestions.has(subQuestionId);
                                                        return `
                                                            <div class="form-check mb-1">
                                                                <input class="form-check-input sub-question-checkbox" type="checkbox"
                                                                       value="${subQuestionId}"
                                                                       id="sq_${dimensionIndex}_${q.numId}_${subIdx}"
                                                                       data-dimension-index="${dimensionIndex}"
                                                                       data-main-question="${q.numId}"
                                                                       data-sub-index="${subIdx}"
                                                                       data-col-index="${q.colIndices[subIdx]}"
                                                                       ${isUsed ? 'disabled' : ''}>
                                                                <label class="form-check-label small ${isUsed ? 'text-muted' : ''}" for="sq_${dimensionIndex}_${q.numId}_${subIdx}">
                                                                    Q${q.numId}.${subIdx + 1} - ${subQ.title}
                                                                    <span class="badge bg-warning ms-1">${getQuestionScaleLevel(q)}级</span>
                                                                    ${isUsed ? ' <span class="text-muted">(已被其他维度使用)</span>' : ''}
                                                                </label>
                                                            </div>
                                                        `;
                                                    }).join('')}
                                                </div>
                                            </div>
                                        `;
                                    } else {
                                        // 普通题目
                                        return `
                                            <div class="form-check mb-2">
                                                <input class="form-check-input question-checkbox" type="checkbox"
                                                       value="${q.numId}" id="q_${dimensionIndex}_${q.numId}"
                                                       data-dimension-index="${dimensionIndex}"
                                                       data-col-index="${q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId}">
                                                <label class="form-check-label" for="q_${dimensionIndex}_${q.numId}">
                                                    <strong>Q${q.numId}</strong> - ${q.title}
                                                    <span class="badge bg-secondary ms-2">${getQuestionTypeDescription(q.type)}</span>
                                                    <span class="badge bg-warning ms-1">${getQuestionScaleLevel(q)}级</span>
                                                </label>
                                            </div>
                                        `;
                                    }
                                }).join('')}
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle"></i> 选择构成此维度的题目
                        </small>
                    </div>

                    <!-- 已选题目显示区域 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">已选题目</label>
                        <div id="selectedQuestions_${dimensionIndex}" class="selected-questions border rounded p-2 bg-light" style="min-height:60px;">
                            <div class="text-muted">请选择题目...</div>
                        </div>
                    </div>

                    <!-- 参数设置区域 -->
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">目标信度</label>
                            <input type="number" class="form-control dimension-alpha" step="0.01" min="0.5" max="1"
                                   value="0.8" placeholder="默认0.8" data-dimension-index="${dimensionIndex}"
                                   onchange="validateInput(this, 0.5, 1, '维度信度系数')">
                            <small class="form-text text-muted">建议范围：0.7以上</small>
                            <div class="invalid-feedback" id="dimension-alpha-${dimensionIndex}-error"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">维度名称（可选）</label>
                            <input type="text" class="form-control dimension-name" placeholder="如：学习动机"
                                   data-dimension-index="${dimensionIndex}">
                            <small class="form-text text-muted">便于识别维度含义</small>
                        </div>
                    </div>

                    <!-- 题目详细设置区域（初始隐藏） -->
                    <div id="questionDetails_${dimensionIndex}" class="question-details mt-3" style="display:none;">
                        <hr>
                        <h6 class="text-secondary">
                            <i class="bi bi-gear"></i> 题目详细设置
                        </h6>
                        <div id="questionDetailsContent_${dimensionIndex}"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    dimensionsList.insertAdjacentHTML('beforeend', dimensionHtml);

    // 绑定题目选择事件
    bindQuestionSelectionEvents(dimensionIndex);
}

// 展开/收起矩阵量表题的小题
function toggleMatrixSubQuestions(dimensionIndex, questionId) {
    const subQuestionsDiv = document.getElementById(`subQuestions_${dimensionIndex}_${questionId}`);
    const toggleBtn = document.getElementById(`toggleBtn_${dimensionIndex}_${questionId}`);
    const icon = toggleBtn.querySelector('i');

    if (subQuestionsDiv.style.display === 'none') {
        subQuestionsDiv.style.display = 'block';
        icon.className = 'bi bi-chevron-down';
    } else {
        subQuestionsDiv.style.display = 'none';
        icon.className = 'bi bi-chevron-right';
    }
}

// 绑定题目选择事件
function bindQuestionSelectionEvents(dimensionIndex) {
    // 绑定普通题目选择事件
    const questionCheckboxes = document.querySelectorAll(`#questionSelector_${dimensionIndex} .question-checkbox`);
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const questionId = parseInt(this.value);

            if (this.checked) {
                // 检查量表级数一致性
                const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
                if (question) {
                    const questionScaleLevel = getQuestionScaleLevel(question);
                    const currentScaleLevel = getCurrentScaleLevel();

                    if (currentScaleLevel !== null && questionScaleLevel !== currentScaleLevel) {
                        alert(`题目 Q${questionId} 的量表级数为${questionScaleLevel}级，与当前量表级数${currentScaleLevel}级不一致。所有题目必须使用相同的量表级数。`);
                        this.checked = false;
                        return;
                    }
                }

                // 添加到已使用题目集合
                window.usedQuestions.add(questionId);
            } else {
                // 从已使用题目集合中移除
                window.usedQuestions.delete(questionId);
            }

            // 更新量表级数显示
            updateScaleInfoDisplay();

            // 更新当前维度的显示
            updateSelectedQuestions(dimensionIndex);

            // 更新其他维度的可选题目
            updateAvailableQuestions();
        });
    });

    // 绑定矩阵小题选择事件
    const subQuestionCheckboxes = document.querySelectorAll(`#questionSelector_${dimensionIndex} .sub-question-checkbox`);
    subQuestionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const questionId = parseInt(this.dataset.mainQuestion);
            const subIndex = parseInt(this.dataset.subIndex);
            const uniqueId = `${questionId}_${subIndex}`;

            if (this.checked) {
                // 检查量表级数一致性
                const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
                if (question) {
                    const questionScaleLevel = getQuestionScaleLevel(question);
                    const currentScaleLevel = getCurrentScaleLevel();

                    if (currentScaleLevel !== null && questionScaleLevel !== currentScaleLevel) {
                        alert(`题目 Q${questionId}.${subIndex + 1} 的量表级数为${questionScaleLevel}级，与当前量表级数${currentScaleLevel}级不一致。所有题目必须使用相同的量表级数。`);
                        this.checked = false;
                        return;
                    }
                }

                // 添加到已使用题目集合（使用唯一标识）
                window.usedQuestions.add(uniqueId);
            } else {
                // 从已使用题目集合中移除
                window.usedQuestions.delete(uniqueId);
            }

            // 更新量表级数显示
            updateScaleInfoDisplay();

            // 更新当前维度的显示
            updateSelectedQuestions(dimensionIndex);

            // 更新其他维度的可选题目
            updateAvailableQuestions();
        });
    });
}

// 更新已选题目显示
function updateSelectedQuestions(dimensionIndex) {
    const questionCheckboxes = document.querySelectorAll(`#questionSelector_${dimensionIndex} .question-checkbox:checked`);
    const subQuestionCheckboxes = document.querySelectorAll(`#questionSelector_${dimensionIndex} .sub-question-checkbox:checked`);
    const selectedQuestionsDiv = document.getElementById(`selectedQuestions_${dimensionIndex}`);
    const questionDetailsDiv = document.getElementById(`questionDetails_${dimensionIndex}`);
    const questionDetailsContent = document.getElementById(`questionDetailsContent_${dimensionIndex}`);

    if (questionCheckboxes.length === 0 && subQuestionCheckboxes.length === 0) {
        selectedQuestionsDiv.innerHTML = '<div class="text-muted">请选择题目...</div>';
        questionDetailsDiv.style.display = 'none';
        return;
    }

    // 收集已选题目和小题
    const selectedItems = [];

    // 处理普通题目
    Array.from(questionCheckboxes).forEach(cb => {
        const questionId = parseInt(cb.value);
        const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
        selectedItems.push({
            id: questionId,
            title: question.title,
            type: 'question',
            colIndex: cb.dataset.colIndex
        });
    });

    // 处理矩阵小题
    Array.from(subQuestionCheckboxes).forEach(cb => {
        const questionId = parseInt(cb.dataset.mainQuestion);
        const subIndex = parseInt(cb.dataset.subIndex);
        const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
        const subQuestion = question.subQuestions[subIndex];
        selectedItems.push({
            id: `${questionId}_${subIndex}`,
            title: `${question.title} - ${subQuestion.title}`,
            type: 'subquestion',
            mainQuestionId: questionId,
            subIndex: subIndex,
            colIndex: cb.dataset.colIndex
        });
    });

    selectedQuestionsDiv.innerHTML = selectedItems.map(item =>
        `<span class="badge bg-primary me-2 mb-2">Q${item.type === 'subquestion' ? item.mainQuestionId + '.' + (item.subIndex + 1) : item.id} - ${item.title}</span>`
    ).join('');

    // 显示题目详细设置
    questionDetailsDiv.style.display = 'block';

    // 生成题目详细设置表格
    let detailsHtml = `
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead class="table-light">
                    <tr>
                        <th style="width:15%;">题目</th>
                        <th style="width:35%;">题目内容</th>
                        <th style="width:20%;">计分方向</th>
                        <th style="width:20%;">目标均值</th>
                        <th style="width:10%;">操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    selectedItems.forEach(item => {
        const displayId = item.type === 'subquestion' ? `${item.mainQuestionId}.${item.subIndex + 1}` : item.id;
        const questionId = item.type === 'subquestion' ? `${item.mainQuestionId}_${item.subIndex}` : item.id;
        const previewId = item.type === 'subquestion' ? item.mainQuestionId : item.id;

        detailsHtml += `
            <tr>
                <td><strong>Q${displayId}</strong></td>
                <td class="text-truncate" style="max-width:200px;" title="${item.title}">${item.title}</td>
                <td>
                    <div class="custom-switch-container">
                        <input class="custom-switch-input scoring-direction-switch" type="checkbox"
                               id="switch_${questionId}_${dimensionIndex}"
                               data-question-id="${questionId}"
                               data-dimension-index="${dimensionIndex}"
                               data-col-index="${item.colIndex}"
                               checked>
                        <label class="custom-switch-label" for="switch_${questionId}_${dimensionIndex}">
                            <span class="switch-text-positive">正向</span>
                            <span class="switch-text-negative">负向</span>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm target-mean"
                           step="0.1" min="1" max="${getMaxScaleLevel()}" placeholder="没有要求可不设置"
                           data-question-id="${questionId}" data-dimension-index="${dimensionIndex}" data-col-index="${item.colIndex}"
                           onchange="validateInput(this, 1, ${getMaxScaleLevel()}, '目标均值')">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-info"
                            onclick="showQuestionPreview(${previewId})" title="预览题目">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    detailsHtml += `
                </tbody>
            </table>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <i class="bi bi-info-circle"></i>
                <strong>计分方向：</strong>正向计分表示选项1=1分，选项2=2分...；（5级量表中）反向计分表示选项1=5分，选项2=4分...<br>
                <strong>目标均值：</strong>设置该题目调整后的平均分，不设置则使用默认值
            </small>
        </div>
    `;

    questionDetailsContent.innerHTML = detailsHtml;
}

// 删除维度
function removeDimension(dimensionIndex) {
    const dimensionItem = document.querySelector(`[data-dimension-index="${dimensionIndex}"]`);
    if (dimensionItem) {
        // 释放该维度使用的普通题目
        const questionCheckboxes = dimensionItem.querySelectorAll('.question-checkbox:checked');
        questionCheckboxes.forEach(checkbox => {
            window.usedQuestions.delete(parseInt(checkbox.value));
        });

        // 释放该维度使用的矩阵小题
        const subQuestionCheckboxes = dimensionItem.querySelectorAll('.sub-question-checkbox:checked');
        subQuestionCheckboxes.forEach(checkbox => {
            window.usedQuestions.delete(checkbox.value); // 这里是字符串格式 "questionId_subIndex"
        });

        dimensionItem.remove();
        // 重新编号
        updateDimensionIndices();
        // 更新其他维度的可选题目
        updateAvailableQuestions();
    }
}

// 更新维度编号
function updateDimensionIndices() {
    const dimensionItems = document.querySelectorAll('.dimension-item');
    dimensionItems.forEach((item, index) => {
        const oldIndex = item.getAttribute('data-dimension-index');
        item.setAttribute('data-dimension-index', index);

        // 更新标题
        const titleElement = item.querySelector('h6');
        if (titleElement) {
            titleElement.innerHTML = `<i class="bi bi-diagram-3"></i> 维度 ${index + 1}`;
        }

        // 更新删除按钮
        const deleteBtn = item.querySelector('button[onclick*="removeDimension"]');
        if (deleteBtn) {
            deleteBtn.setAttribute('onclick', `removeDimension(${index})`);
        }

        // 更新所有相关元素的data-dimension-index
        const elementsToUpdate = item.querySelectorAll('[data-dimension-index]');
        elementsToUpdate.forEach(el => {
            el.setAttribute('data-dimension-index', index);
        });

        // 更新ID
        const elementsWithId = item.querySelectorAll(`[id*="_${oldIndex}"]`);
        elementsWithId.forEach(el => {
            const oldId = el.id;
            const newId = oldId.replace(`_${oldIndex}`, `_${index}`);
            el.id = newId;
        });

        // 更新for属性
        const labelsWithFor = item.querySelectorAll(`[for*="_${oldIndex}_"]`);
        labelsWithFor.forEach(label => {
            const oldFor = label.getAttribute('for');
            const newFor = oldFor.replace(`_${oldIndex}_`, `_${index}_`);
            label.setAttribute('for', newFor);
        });

        // 更新checkbox的id
        const checkboxes = item.querySelectorAll(`[id*="q_${oldIndex}_"]`);
        checkboxes.forEach(cb => {
            const oldId = cb.id;
            const newId = oldId.replace(`q_${oldIndex}_`, `q_${index}_`);
            cb.id = newId;
        });
    });
}

// 更新可选题目（当维度删除后）
function updateAvailableQuestions() {
    const dimensionItems = document.querySelectorAll('.dimension-item');
    const allowedTypes = ["3", "5", "6single"];
    const filteredQuestions = surveyStructureInfoForAdjustment.filter(q => allowedTypes.includes(q.type));

    dimensionItems.forEach((item, dimensionIndex) => {
        const questionSelector = item.querySelector(`#questionSelector_${dimensionIndex}`);
        if (questionSelector) {
            // 保存当前状态
            const currentQuestionSelected = Array.from(item.querySelectorAll('.question-checkbox:checked')).map(cb => parseInt(cb.value));
            const currentSubQuestionSelected = Array.from(item.querySelectorAll('.sub-question-checkbox:checked')).map(cb => cb.value);
            const expandedMatrixQuestions = Array.from(item.querySelectorAll('[id^="subQuestions_"][style*="block"]')).map(div => {
                const match = div.id.match(/subQuestions_\d+_(\d+)/);
                return match ? parseInt(match[1]) : null;
            }).filter(id => id !== null);

            // 过滤可用题目的逻辑：
            // 1. 如果是普通题目，检查题目ID是否被使用
            // 2. 如果是矩阵题目，只要有任何小题可用就显示整个题目
            // 3. 当前维度已选的题目总是显示
            const availableQuestions = filteredQuestions.filter(q => {
                const questionSelected = currentQuestionSelected.includes(q.numId);

                // 如果当前维度已选择此题目，总是显示
                if (questionSelected) {
                    return true;
                }

                // 检查普通题目是否被其他维度使用
                const questionUsedByOthers = window.usedQuestions.has(q.numId);

                // 如果是矩阵题目，检查是否还有可用的小题
                if (q.subQuestions && q.subQuestions.length > 0) {
                    // 检查是否有小题被当前维度选中
                    let hasSelectedSubQuestions = false;
                    for (let i = 0; i < q.subQuestions.length; i++) {
                        if (currentSubQuestionSelected.includes(`${q.numId}_${i}`)) {
                            hasSelectedSubQuestions = true;
                            break;
                        }
                    }

                    // 如果当前维度有选中的小题，总是显示
                    if (hasSelectedSubQuestions) {
                        return true;
                    }

                    // 检查是否还有未被使用的小题
                    let hasAvailableSubQuestions = false;
                    for (let i = 0; i < q.subQuestions.length; i++) {
                        if (!window.usedQuestions.has(`${q.numId}_${i}`)) {
                            hasAvailableSubQuestions = true;
                            break;
                        }
                    }

                    return hasAvailableSubQuestions;
                } else {
                    // 普通题目：只要没被其他维度使用就显示
                    return !questionUsedByOthers;
                }
            });

            // 重新生成选项
            questionSelector.innerHTML = availableQuestions.map(q => {
                // 检查是否为矩阵量表题
                if ((q.type === '6single' || q.type === '3' || q.type === '5') && q.subQuestions && q.subQuestions.length > 0) {
                    const isExpanded = expandedMatrixQuestions.includes(q.numId);
                    // 矩阵量表题，显示可展开的小题
                    return `
                        <div class="form-check mb-2">
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-sm btn-outline-secondary me-2"
                                        onclick="toggleMatrixSubQuestions(${dimensionIndex}, ${q.numId})"
                                        id="toggleBtn_${dimensionIndex}_${q.numId}">
                                    <i class="bi bi-chevron-${isExpanded ? 'down' : 'right'}"></i>
                                </button>
                                <strong>Q${q.numId}</strong> - ${q.title}
                                <span class="badge bg-secondary ms-2">${getQuestionTypeDescription(q.type)}</span>
                                <span class="badge bg-info ms-2">${q.subQuestions.length}个小题</span>
                            </div>
                            <div id="subQuestions_${dimensionIndex}_${q.numId}" class="ms-4 mt-2" style="display:${isExpanded ? 'block' : 'none'};">
                                ${q.subQuestions.map((subQ, subIdx) => {
                                    const subQuestionId = `${q.numId}_${subIdx}`;
                                    const isSubSelected = currentSubQuestionSelected.includes(subQuestionId);
                                    const isSubUsedByOthers = window.usedQuestions.has(subQuestionId) && !isSubSelected;

                                    return `
                                        <div class="form-check mb-1">
                                            <input class="form-check-input sub-question-checkbox" type="checkbox"
                                                   value="${subQuestionId}"
                                                   id="sq_${dimensionIndex}_${q.numId}_${subIdx}"
                                                   data-dimension-index="${dimensionIndex}"
                                                   data-main-question="${q.numId}"
                                                   data-sub-index="${subIdx}"
                                                   data-col-index="${q.colIndices[subIdx]}"
                                                   ${isSubSelected ? 'checked' : ''}
                                                   ${isSubUsedByOthers ? 'disabled' : ''}>
                                            <label class="form-check-label small ${isSubUsedByOthers ? 'text-muted' : ''}" for="sq_${dimensionIndex}_${q.numId}_${subIdx}">
                                                Q${q.numId}.${subIdx + 1} - ${subQ.title}
                                                <span class="badge bg-warning ms-1">${getQuestionScaleLevel(q)}级</span>
                                                ${isSubUsedByOthers ? ' <span class="text-muted">(已被其他维度使用)</span>' : ''}
                                            </label>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                } else {
                    // 普通题目
                    const isSelected = currentQuestionSelected.includes(q.numId);
                    return `
                        <div class="form-check mb-2">
                            <input class="form-check-input question-checkbox" type="checkbox"
                                   value="${q.numId}" id="q_${dimensionIndex}_${q.numId}"
                                   data-dimension-index="${dimensionIndex}"
                                   data-col-index="${q.colIndices && q.colIndices.length > 0 ? q.colIndices[0] : q.numId}"
                                   ${isSelected ? 'checked' : ''}>
                            <label class="form-check-label" for="q_${dimensionIndex}_${q.numId}">
                                <strong>Q${q.numId}</strong> - ${q.title}
                                <span class="badge bg-secondary ms-2">${getQuestionTypeDescription(q.type)}</span>
                                <span class="badge bg-warning ms-1">${getQuestionScaleLevel(q)}级</span>
                            </label>
                        </div>
                    `;
                }
            }).join('');

            // 重新绑定事件
            bindQuestionSelectionEvents(dimensionIndex);
        }
    });
}

// 题目预览功能
function showQuestionPreview(questionId) {
    const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
    if (!question) return;

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">题目预览 - Q${questionId}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>题目内容：</h6>
                    <p class="border p-3 bg-light">${question.title}</p>
                    <h6>题目类型：</h6>
                    <p>${getQuestionTypeDescription(question.type)}</p>
                    ${question.options ? `
                        <h6>选项：</h6>
                        <ul>
                            ${question.options.map((opt, idx) => `<li>${idx + 1}. ${opt}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// 获取题目类型描述
function getQuestionTypeDescription(type) {
    const typeMap = {
        '1': '填空题',
        '2': '填空题',
        '3': '单选题',
        '4': '多选题',
        '5': '单项量表题',
        '6single': '矩阵单选题',
        '6multiple': '矩阵多选题',
        '7': '下拉题',
        '8': '单项滑条题',
        '9duotian1': '多填空题1',
        '9duotian2': '多填空题2',
        '9hua': '矩阵滑条题',
        '11': '排序题',
        '12': '比重滑条题'
    };
    return typeMap[type] || '未知题型';
}

// 执行数据调整
async function runAdjustment() {
    const type = document.getElementById('adjustmentType').value;

    if (type === 'multiDimensionalScale') {
        await runMultiDimensionalScaleAdjustment();
    }
}

// 执行分维度量表调整
async function runMultiDimensionalScaleAdjustment() {
    try {
        // 收集维度定义
        const dimensions = [];
        const targetDimensionAlphas = [];
        const targetItemMeans = [];
        const scoringDirections = [];
        const dimensionItems = document.querySelectorAll('.dimension-item');

        if (dimensionItems.length === 0) {
            alert('请至少添加一个维度');
            return;
        }

        for (let i = 0; i < dimensionItems.length; i++) {
            const dimensionItem = dimensionItems[i];

            // 获取选中的题目和小题
            const selectedQuestionCheckboxes = dimensionItem.querySelectorAll('.question-checkbox:checked');
            const selectedSubQuestionCheckboxes = dimensionItem.querySelectorAll('.sub-question-checkbox:checked');

            if (selectedQuestionCheckboxes.length === 0 && selectedSubQuestionCheckboxes.length === 0) {
                alert(`维度 ${i + 1} 请至少选择一个题目或小题`);
                return;
            }

            // 收集所有选中的列索引（用于后端处理）
            const selectedColumns = [];
            const dimensionScoringDirections = [];
            const dimensionTargetMeans = [];

            // 处理普通题目
            Array.from(selectedQuestionCheckboxes).forEach(cb => {
                const colIndex = parseInt(cb.dataset.colIndex);
                selectedColumns.push(colIndex);

                // 计分方向（从开关读取）
                const scoringSwitch = dimensionItem.querySelector(`[data-question-id="${cb.value}"].scoring-direction-switch`);
                const scoringDirection = (scoringSwitch && scoringSwitch.checked) ? 'positive' : 'negative';
                dimensionScoringDirections.push(scoringDirection);

                // 目标均值
                const meanInput = dimensionItem.querySelector(`[data-question-id="${cb.value}"].target-mean`);
                const targetMean = meanInput && meanInput.value ? parseFloat(meanInput.value) : null;
                dimensionTargetMeans.push(targetMean);
            });

            // 处理矩阵小题
            Array.from(selectedSubQuestionCheckboxes).forEach(cb => {
                const colIndex = parseInt(cb.dataset.colIndex);
                selectedColumns.push(colIndex);

                const questionId = `${cb.dataset.mainQuestion}_${cb.dataset.subIndex}`;

                // 计分方向（从开关读取）
                const scoringSwitch = dimensionItem.querySelector(`[data-question-id="${questionId}"].scoring-direction-switch`);
                const scoringDirection = (scoringSwitch && scoringSwitch.checked) ? 'positive' : 'negative';
                dimensionScoringDirections.push(scoringDirection);

                // 目标均值
                const meanInput = dimensionItem.querySelector(`[data-question-id="${questionId}"].target-mean`);
                const targetMean = meanInput && meanInput.value ? parseFloat(meanInput.value) : null;
                dimensionTargetMeans.push(targetMean);
            });

            dimensions.push(selectedColumns);

            // 获取维度目标信度
            const alphaInput = dimensionItem.querySelector('.dimension-alpha');
            targetDimensionAlphas.push(parseFloat(alphaInput.value) || 0.8);

            scoringDirections.push(dimensionScoringDirections);
            targetItemMeans.push(dimensionTargetMeans);
        }

        // 收集其他参数
        const targetTotalAlpha = parseFloat(document.getElementById('targetTotalAlpha').value) || 0.85;
        const targetKMO = parseFloat(document.getElementById('targetKMO').value) || 0.7;
        const targetInterDimensionCorrelation = parseFloat(document.getElementById('targetInterDimensionCorrelation').value) || 0.4;
        const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.02;

        // 参数验证
        const validationResult = validateMultiDimensionalScaleParams({
            targetDimensionAlphas,
            targetTotalAlpha,
            targetKMO,
            targetInterDimensionCorrelation,
            tolerance,
            targetItemMeans
        });

        if (!validationResult.valid) {
            alert('参数验证失败：' + validationResult.message);
            return;
        }

        // 获取当前已选题目的量表级数
        const scaleLevel = getCurrentScaleLevel();

        if (scaleLevel === null) {
            alert('请先选择题目以确定量表级数');
            return;
        }

        const params = {
            sessionId: currentSessionId,
            dimensions: dimensions,
            scaleLevel: scaleLevel,
            targetDimensionAlphas: targetDimensionAlphas,
            targetTotalAlpha: targetTotalAlpha,
            targetKMO: targetKMO,
            targetInterDimensionCorrelation: targetInterDimensionCorrelation,
            tolerance: tolerance,
            targetItemMeans: targetItemMeans.some(dim => dim.some(mean => mean !== null)) ? targetItemMeans : null,
            scoringDirections: scoringDirections
        };

        console.log('调整参数:', params);

        // 验证参数
        if (!validateAdjustmentParams(params)) {
            return;
        }

        // 显示加载动效
        showLoadingOverlay('正在执行分维度量表调整', '请稍候，系统正在处理您的数据...');

        // 发送调整请求
        const response = await fetch('/ai-chat/adjustment/multi-dimensional-scale', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.text();

        // 关闭弹窗
        const modal = bootstrap.Modal.getInstance(document.getElementById('adjustmentParamModal'));
        if (modal) {
            modal.hide();
        }

        // 生成用户友好的参数描述
        const dimensionDescriptions = dimensions.map((dim, idx) => {
            const dimensionName = dimensionItems[idx].querySelector('.dimension-name').value || `维度${idx + 1}`;
            return `${dimensionName}(Q${dim.join(',Q')}): 目标信度${targetDimensionAlphas[idx]}`;
        }).join('\n');

        // 更新"等待AI填充"的消息为系统消息
        await updateWaitingMessageToSystem('分维度调整量表');

        // 添加AI助手的调整结果
        addMessage('assistant', result);

        // 刷新聊天框和表格
        refreshChatAndTable();

        // 更新代币余额
        updateTokenBalance();

    } catch (error) {
        console.error('调整请求失败:', error);
        alert('调整请求失败: ' + error.message);
    } finally {
        // 隐藏加载动效
        hideLoadingOverlay();
    }
}

// 验证调整参数
function validateAdjustmentParams(params) {
    // 检查维度是否有重复题目
    const allQuestions = params.dimensions.flat();
    const uniqueQuestions = new Set(allQuestions);

    if (allQuestions.length !== uniqueQuestions.size) {
        alert('错误：存在重复的题目，每个题目只能属于一个维度');
        return false;
    }

    // 检查信度值范围
    for (let i = 0; i < params.targetDimensionAlphas.length; i++) {
        const alpha = params.targetDimensionAlphas[i];
        if (alpha < 0.5 || alpha > 1) {
            alert(`错误：维度 ${i + 1} 的目标信度值应在0.5-1.0之间`);
            return false;
        }
    }

    // 检查总信度值
    if (params.targetTotalAlpha < 0.5 || params.targetTotalAlpha > 1) {
        alert('错误：总量表目标信度值应在0.5-1.0之间');
        return false;
    }

    // 检查KMO值
    if (params.targetKMO < 0.5 || params.targetKMO > 1) {
        alert('错误：目标KMO值应在0.5-1.0之间');
        return false;
    }

    // 检查目标均值
    if (params.targetItemMeans) {
        for (let i = 0; i < params.targetItemMeans.length; i++) {
            for (let j = 0; j < params.targetItemMeans[i].length; j++) {
                const mean = params.targetItemMeans[i][j];
                if (mean !== null) {
                    // 获取当前量表级数
                    const currentScale = getCurrentScaleLevel() || getMaxScaleLevel();
                    if (mean < 1 || mean > currentScale) {
                        alert(`错误：维度 ${i + 1} 中题目的目标均值应在1-${currentScale}之间`);
                        return false;
                    }
                }
            }
        }
    }

    return true;
}

// 动态获取量表的最大级数
function getMaxScaleLevel() {
    if (!surveyStructureInfoForAdjustment || !Array.isArray(surveyStructureInfoForAdjustment)) {
        return 7; // 默认7级
    }

    let maxScale = 5; // 默认5级

    // 遍历所有量表题目，找出最大的选项数量
    surveyStructureInfoForAdjustment.forEach(q => {
        if ((q.type === '5' || q.type === '3' || q.type === '6single') && q.options && Array.isArray(q.options)) {
            maxScale = Math.max(maxScale, q.options.length);
        }

        // 对于矩阵量表题，检查子题的选项数量
        if (q.subQuestions && Array.isArray(q.subQuestions)) {
            q.subQuestions.forEach(subQ => {
                if (subQ.options && Array.isArray(subQ.options)) {
                    maxScale = Math.max(maxScale, subQ.options.length);
                }
            });
        }
    });

    return maxScale;
}

// 验证分维度调整量表参数
function validateMultiDimensionalScaleParams(params) {
    const {
        targetDimensionAlphas,
        targetTotalAlpha,
        targetKMO,
        targetInterDimensionCorrelation,
        tolerance,
        targetItemMeans
    } = params;

    // 验证维度信度系数
    for (let i = 0; i < targetDimensionAlphas.length; i++) {
        const alpha = targetDimensionAlphas[i];
        if (isNaN(alpha)) {
            return { valid: false, message: `维度${i + 1}的信度系数必须是数字` };
        }
        if (alpha <= 0 || alpha > 1) {
            return { valid: false, message: `维度${i + 1}的信度系数必须在0到1之间，当前值：${alpha}` };
        }
        if (alpha < 0.5) {
            return { valid: false, message: `维度${i + 1}的信度系数过低（${alpha}），建议设置在0.6以上` };
        }
    }

    // 验证总信度系数
    if (isNaN(targetTotalAlpha)) {
        return { valid: false, message: '总信度系数必须是数字' };
    }
    if (targetTotalAlpha <= 0 || targetTotalAlpha > 1) {
        return { valid: false, message: `总信度系数必须在0到1之间，当前值：${targetTotalAlpha}` };
    }
    if (targetTotalAlpha < 0.6) {
        return { valid: false, message: `总信度系数过低（${targetTotalAlpha}），建议设置在0.7以上` };
    }

    // 验证KMO值
    if (isNaN(targetKMO)) {
        return { valid: false, message: 'KMO值必须是数字' };
    }
    if (targetKMO <= 0 || targetKMO > 1) {
        return { valid: false, message: `KMO值必须在0到1之间，当前值：${targetKMO}` };
    }
    if (targetKMO < 0.5) {
        return { valid: false, message: `KMO值过低（${targetKMO}），建议设置在0.6以上` };
    }

    // 验证维度间相关性
    if (isNaN(targetInterDimensionCorrelation)) {
        return { valid: false, message: '维度间相关性必须是数字' };
    }
    if (targetInterDimensionCorrelation < 0 || targetInterDimensionCorrelation > 1) {
        return { valid: false, message: `维度间相关性必须在0到1之间，当前值：${targetInterDimensionCorrelation}` };
    }

    // 验证容差
    if (isNaN(tolerance)) {
        return { valid: false, message: '容差必须是数字' };
    }
    if (tolerance <= 0 || tolerance > 0.5) {
        return { valid: false, message: `容差必须在0到0.5之间，当前值：${tolerance}` };
    }

    // 验证目标均值（如果设置了）
    if (targetItemMeans) {
        for (let i = 0; i < targetItemMeans.length; i++) {
            const dimensionMeans = targetItemMeans[i];
            for (let j = 0; j < dimensionMeans.length; j++) {
                const mean = dimensionMeans[j];
                if (mean !== null && mean !== undefined) {
                    if (isNaN(mean)) {
                        return { valid: false, message: `维度${i + 1}题目${j + 1}的目标均值必须是数字` };
                    }
                    if (mean < 1 || mean > 7) {
                        return { valid: false, message: `维度${i + 1}题目${j + 1}的目标均值应在1到7之间，当前值：${mean}` };
                    }
                }
            }
        }
    }

    return { valid: true };
}

// 实时验证输入框
function validateInput(input, min, max, fieldName) {
    const value = parseFloat(input.value);
    const errorDiv = document.getElementById(input.id + '-error');

    // 清除之前的错误状态
    input.classList.remove('is-invalid');
    if (errorDiv) {
        errorDiv.textContent = '';
    }

    if (isNaN(value)) {
        showInputError(input, errorDiv, `${fieldName}必须是数字`);
        return false;
    }

    if (value < min || value > max) {
        showInputError(input, errorDiv, `${fieldName}必须在${min}到${max}之间`);
        return false;
    }

    // 特殊验证规则
    if (fieldName.includes('信度') && value < 0.6) {
        showInputWarning(input, errorDiv, `${fieldName}建议设置在0.6以上`);
    } else if (fieldName === 'KMO值' && value < 0.6) {
        showInputWarning(input, errorDiv, 'KMO值建议设置在0.6以上');
    }

    return true;
}

// 显示输入错误
function showInputError(input, errorDiv, message) {
    input.classList.add('is-invalid');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.className = 'invalid-feedback d-block';
    }
}

// 显示输入警告
function showInputWarning(input, errorDiv, message) {
    input.classList.add('is-valid');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.className = 'text-warning small d-block';
    }
}

// 调整工具栏按钮布局
function adjustToolbarLayout() {
    const toolbar = document.querySelector('.toolbar-right');
    if (!toolbar) return;

    const buttons = toolbar.querySelectorAll('.btn');
    const buttonCount = buttons.length;
    const screenWidth = window.innerWidth;

    // 移除所有布局类
    toolbar.classList.remove('grid-layout', 'grid-2col', 'grid-1col');

    // 根据屏幕宽度和按钮数量决定布局
    if (screenWidth <= 480) {
        // 超小屏幕：单列布局
        if (buttonCount >= 3) {
            toolbar.classList.add('grid-layout', 'grid-1col');
        }
    } else if (screenWidth <= 768) {
        // 小屏幕：最多2列
        if (buttonCount >= 4) {
            toolbar.classList.add('grid-layout', 'grid-2col');
        }
    } else {
        // 大屏幕：根据按钮数量决定
        if (buttonCount === 6) {
            // 6个按钮使用2×3布局
            toolbar.classList.add('grid-layout');
        } else if (buttonCount >= 5) {
            // 5个或更多按钮使用网格布局
            toolbar.classList.add('grid-layout');
        }
        // 4个或更少按钮保持flex布局
    }
}

// 页面加载完成后调整布局
document.addEventListener('DOMContentLoaded', function() {
    adjustToolbarLayout();

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        adjustToolbarLayout();
    });

    // 监听按钮变化（如果有动态添加/删除按钮的情况）
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                adjustToolbarLayout();
            }
        });
    });

    const toolbar = document.querySelector('.toolbar-right');
    if (toolbar) {
        observer.observe(toolbar, { childList: true, subtree: true });
    }
});

// 显示加载动效
function showLoadingOverlay(title = '处理中', subtitle = '请稍候...') {
    // 移除已存在的加载动效
    hideLoadingOverlay();

    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.id = 'loadingOverlay';

    overlay.innerHTML = `
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">${title}</div>
            <div class="loading-subtext">${subtitle}<span class="loading-dots">...</span></div>
        </div>
    `;

    document.body.appendChild(overlay);

    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

// 隐藏加载动效
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
            // 恢复背景滚动
            document.body.style.overflow = '';
        }, 300);
    }
}

// 更新"等待AI填充"的消息为系统消息
async function updateWaitingMessageToSystem(adjustmentType) {
    try {
        const response = await fetch('/ai-chat/update-waiting-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sessionId: currentSessionId,
                adjustmentType: adjustmentType
            })
        });

        if (!response.ok) {
            console.error('更新等待消息失败:', response.status);
        }
    } catch (error) {
        console.error('更新等待消息失败:', error);
    }
}

// 刷新聊天框和表格
function refreshChatAndTable() {
    try {
        // 直接刷新当前会话，这样会同时更新聊天消息、表格数据和版本管理
        if (currentSessionId) {
            selectSession(currentSessionId);
            // 延迟刷新会话列表，确保后端操作完成
            setTimeout(() => {
                loadSessions();
            }, 1000);
        }
    } catch (error) {
        console.error('刷新会话失败:', error);
    }
}

// 导出配置
function exportConfig() {
    try {
        const config = collectCurrentConfig();
        if (!config) {
            alert('请先配置至少一个维度');
            return;
        }

        const configJson = JSON.stringify(config, null, 2);

        // 复制到剪贴板
        navigator.clipboard.writeText(configJson).then(() => {
            alert('分维度调整量表配置已复制到剪贴板！');
        }).catch(() => {
            // 如果复制失败，显示在模态框中供手动复制
            showConfigModal('导出分维度调整量表配置', configJson, true);
        });

    } catch (error) {
        console.error('导出配置失败:', error);
        alert('导出配置失败: ' + error.message);
    }
}

// 导入配置
function importConfig() {
    showConfigModal('导入分维度调整量表配置', '', false);
}

// 显示配置模态框
function showConfigModal(title, content, readonly) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">配置JSON：</label>
                        <textarea class="form-control" id="configTextarea" rows="15"
                                  style="font-family: monospace; font-size: 12px;"
                                  ${readonly ? 'readonly' : ''}>${content}</textarea>
                    </div>
                    ${readonly ? `
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> 配置已自动复制到剪贴板！您也可以手动复制上面的内容
                        </div>
                    ` : `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>注意：</strong>导入配置将覆盖当前所有设置！<br>
                            <strong>操作步骤：</strong><br>
                            1. 将配置JSON内容粘贴到上方文本框中<br>
                            2. 点击"导入配置"按钮完成导入
                        </div>
                    `}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    ${readonly ? `
                        <button type="button" class="btn btn-primary" onclick="copyToClipboard(event)">复制到剪贴板</button>
                    ` : `
                        <button type="button" class="btn btn-warning" onclick="applyImportedConfig()">导入配置</button>
                    `}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 如果是导出，自动复制到剪贴板
    if (readonly && content) {
        navigator.clipboard.writeText(content).catch(() => {
            console.log('无法自动复制到剪贴板');
        });
    }

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// 收集当前配置
function collectCurrentConfig() {
    const dimensionItems = document.querySelectorAll('.dimension-item');
    if (dimensionItems.length === 0) {
        return null;
    }

    const config = {
        configType: "multiDimensionalScale",
        description: "分维度调整量表配置",
        version: "1.0",
        timestamp: new Date().toISOString(),
        globalParams: {
            scaleLevel: getCurrentScaleLevel() || getMaxScaleLevel(),
            targetTotalAlpha: parseFloat(document.getElementById('targetTotalAlpha').value) || 0.85,
            targetKMO: parseFloat(document.getElementById('targetKMO').value) || 0.7,
            targetInterDimensionCorrelation: parseFloat(document.getElementById('targetInterDimensionCorrelation').value) || 0.4,
            tolerance: parseFloat(document.getElementById('tolerance').value) || 0.02
        },
        dimensions: []
    };

    dimensionItems.forEach((item, index) => {
        const selectedQuestionCheckboxes = item.querySelectorAll('.question-checkbox:checked');
        const selectedSubQuestionCheckboxes = item.querySelectorAll('.sub-question-checkbox:checked');

        if (selectedQuestionCheckboxes.length === 0 && selectedSubQuestionCheckboxes.length === 0) return;

        const dimensionName = item.querySelector('.dimension-name').value || `维度${index + 1}`;
        const targetAlpha = parseFloat(item.querySelector('.dimension-alpha').value) || 0.8;

        // 收集所有选中的列索引和详细信息
        const selectedColumns = [];
        const questionDetails = [];

        // 处理普通题目
        Array.from(selectedQuestionCheckboxes).forEach(cb => {
            const questionId = parseInt(cb.value);
            const colIndex = parseInt(cb.dataset.colIndex);
            selectedColumns.push(colIndex);

            const scoringSwitch = item.querySelector(`[data-question-id="${questionId}"].scoring-direction-switch`);
            const meanInput = item.querySelector(`[data-question-id="${questionId}"].target-mean`);

            questionDetails.push({
                type: 'question',
                questionId: questionId,
                colIndex: colIndex,
                scoringDirection: (scoringSwitch && scoringSwitch.checked) ? 'positive' : 'negative',
                targetMean: meanInput && meanInput.value ? parseFloat(meanInput.value) : null
            });
        });

        // 处理矩阵小题
        Array.from(selectedSubQuestionCheckboxes).forEach(cb => {
            const mainQuestionId = parseInt(cb.dataset.mainQuestion);
            const subIndex = parseInt(cb.dataset.subIndex);
            const colIndex = parseInt(cb.dataset.colIndex);
            const uniqueId = `${mainQuestionId}_${subIndex}`;
            selectedColumns.push(colIndex);

            const scoringSwitch = item.querySelector(`[data-question-id="${uniqueId}"].scoring-direction-switch`);
            const meanInput = item.querySelector(`[data-question-id="${uniqueId}"].target-mean`);

            questionDetails.push({
                type: 'subquestion',
                mainQuestionId: mainQuestionId,
                subIndex: subIndex,
                colIndex: colIndex,
                scoringDirection: (scoringSwitch && scoringSwitch.checked) ? 'positive' : 'negative',
                targetMean: meanInput && meanInput.value ? parseFloat(meanInput.value) : null
            });
        });

        config.dimensions.push({
            name: dimensionName,
            columns: selectedColumns, // 使用列索引而不是题目ID
            targetAlpha: targetAlpha,
            questionDetails: questionDetails
        });
    });

    return config;
}

// 应用导入的配置
function applyImportedConfig() {
    try {
        const textarea = document.getElementById('configTextarea');
        if (!textarea) {
            alert('找不到配置输入框');
            return;
        }

        const configJson = textarea.value.trim();

        if (!configJson) {
            alert('请先在上方文本框中粘贴配置JSON内容');
            textarea.focus();
            return;
        }

        let config;
        try {
            config = JSON.parse(configJson);
        } catch (parseError) {
            alert('配置JSON格式错误，请检查格式是否正确');
            console.error('JSON解析错误:', parseError);
            return;
        }

        // 验证配置格式
        if (!validateConfigFormat(config)) {
            return;
        }

        // 关闭配置导入模态框
        const configModal = bootstrap.Modal.getInstance(textarea.closest('.modal'));
        if (configModal) {
            configModal.hide();
        }

        // 应用配置
        applyConfig(config);

        alert('配置导入成功！');

    } catch (error) {
        console.error('导入配置失败:', error);
        alert('导入配置失败: ' + error.message);
    }
}



// 复制到剪贴板
function copyToClipboard(event) {
    const textarea = document.getElementById('configTextarea');
    const content = textarea.value;

    // 使用现代API复制
    navigator.clipboard.writeText(content).then(() => {
        // 显示提示
        const btn = event ? event.target : document.querySelector('.modal .btn-primary');
        if (btn) {
            const originalText = btn.textContent;
            btn.textContent = '已复制！';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');

            setTimeout(() => {
                btn.textContent = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
            }, 2000);
        }
    }).catch(() => {
        // 降级到旧方法
        textarea.select();
        document.execCommand('copy');
        alert('内容已复制到剪贴板');
    });
}

// 验证配置格式
function validateConfigFormat(config) {
    if (!config || typeof config !== 'object') {
        alert('配置格式错误：不是有效的JSON对象');
        return false;
    }

    // 验证配置类型
    if (config.configType && config.configType !== 'multiDimensionalScale') {
        alert('配置格式错误：这不是分维度调整量表的配置文件');
        return false;
    }

    if (!config.globalParams || typeof config.globalParams !== 'object') {
        alert('配置格式错误：缺少globalParams');
        return false;
    }

    // 验证全局参数的数值范围
    const globalParams = config.globalParams;

    // 验证总量表目标信度
    if (globalParams.targetTotalAlpha !== undefined) {
        const value = parseFloat(globalParams.targetTotalAlpha);
        if (isNaN(value) || value <= 0.5 || value > 1) {
            alert('配置验证失败：总量表目标信度必须在0.5到1之间，当前值：' + globalParams.targetTotalAlpha);
            return false;
        }
        if (value < 0.8) {
            if (!confirm(`总量表目标信度为${value}，低于建议值0.8，是否继续导入？`)) {
                return false;
            }
        }
    }

    // 验证目标KMO值
    if (globalParams.targetKMO !== undefined) {
        const value = parseFloat(globalParams.targetKMO);
        if (isNaN(value) || value < 0.5 || value > 1) {
            alert('配置验证失败：目标KMO值必须在0.5到1之间，当前值：' + globalParams.targetKMO);
            return false;
        }
        if (value < 0.6) {
            if (!confirm(`目标KMO值为${value}，低于建议值0.6，是否继续导入？`)) {
                return false;
            }
        }
    }

    // 验证维度间目标相关系数
    if (globalParams.targetInterDimensionCorrelation !== undefined) {
        const value = parseFloat(globalParams.targetInterDimensionCorrelation);
        if (isNaN(value) || value < 0 || value > 1) {
            alert('配置验证失败：维度间目标相关系数必须在0到1之间，当前值：' + globalParams.targetInterDimensionCorrelation);
            return false;
        }
        if (value < 0.3 || value > 0.7) {
            if (!confirm(`维度间目标相关系数为${value}，超出建议范围0.3-0.7，是否继续导入？`)) {
                return false;
            }
        }
    }

    // 验证允许误差范围
    if (globalParams.tolerance !== undefined) {
        const value = parseFloat(globalParams.tolerance);
        if (isNaN(value) || value <= 0.001 || value > 0.5) {
            alert('配置验证失败：允许误差范围必须在0.001到0.5之间，当前值：' + globalParams.tolerance);
            return false;
        }
    }

    if (!Array.isArray(config.dimensions)) {
        alert('配置格式错误：dimensions必须是数组');
        return false;
    }

    if (config.dimensions.length === 0) {
        alert('配置格式错误：至少需要一个维度');
        return false;
    }

    if (config.dimensions.length > 10) {
        alert('配置验证失败：维度数量不能超过10个，当前数量：' + config.dimensions.length);
        return false;
    }

    // 收集所有使用的题目，检查重复
    const usedQuestions = new Set();
    let detectedScaleLevel = null;

    // 验证每个维度
    for (let i = 0; i < config.dimensions.length; i++) {
        const dim = config.dimensions[i];

        // 支持新格式（columns）和旧格式（questions）
        const hasColumns = Array.isArray(dim.columns) && dim.columns.length > 0;
        const hasQuestions = Array.isArray(dim.questions) && dim.questions.length > 0;

        if (!hasColumns && !hasQuestions) {
            alert(`配置格式错误：维度${i + 1}必须包含columns或questions字段，且不能为空`);
            return false;
        }

        // 验证维度目标信度
        if (dim.targetAlpha !== undefined) {
            const value = parseFloat(dim.targetAlpha);
            if (isNaN(value) || value <= 0.5 || value > 1) {
                alert(`配置验证失败：维度${i + 1}的目标信度必须在0.5到1之间，当前值：${dim.targetAlpha}`);
                return false;
            }
            if (value < 0.7) {
                if (!confirm(`维度${i + 1}的目标信度为${value}，低于建议值0.7，是否继续导入？`)) {
                    return false;
                }
            }
        }

        if (!Array.isArray(dim.questionDetails) || dim.questionDetails.length === 0) {
            alert(`配置格式错误：维度${i + 1}的questionDetails必须是非空数组`);
            return false;
        }

        // 验证questionDetails数量与columns/questions数量匹配
        const expectedCount = hasColumns ? dim.columns.length : dim.questions.length;
        if (dim.questionDetails.length !== expectedCount) {
            alert(`配置格式错误：维度${i + 1}的questionDetails数量与题目数量不匹配`);
            return false;
        }

        // 验证每个题目详情
        for (let j = 0; j < dim.questionDetails.length; j++) {
            const detail = dim.questionDetails[j];

            // 检查题目重复使用
            let questionKey;
            if (detail.type === 'subquestion') {
                questionKey = `${detail.mainQuestionId}_${detail.subIndex}`;
            } else {
                questionKey = detail.questionId;
            }

            if (usedQuestions.has(questionKey)) {
                alert(`配置验证失败：题目${questionKey}被重复使用，每个题目只能分配给一个维度`);
                return false;
            }
            usedQuestions.add(questionKey);

            // 验证目标均值（如果存在）
            if (detail.targetMean !== undefined && detail.targetMean !== null) {
                const meanValue = parseFloat(detail.targetMean);
                if (isNaN(meanValue)) {
                    alert(`配置验证失败：维度${i + 1}题目${j + 1}的目标均值必须是数字，当前值：${detail.targetMean}`);
                    return false;
                }

                // 根据当前问卷结构检查量表级数
                if (surveyStructureInfoForAdjustment && Array.isArray(surveyStructureInfoForAdjustment)) {
                    let questionId = detail.type === 'subquestion' ? detail.mainQuestionId : detail.questionId;
                    const question = surveyStructureInfoForAdjustment.find(q => q.numId === questionId);
                    if (question) {
                        const scaleLevel = getQuestionScaleLevel(question);
                        if (detectedScaleLevel === null) {
                            detectedScaleLevel = scaleLevel;
                        } else if (detectedScaleLevel !== scaleLevel) {
                            alert(`配置验证失败：检测到不同的量表级数，所有题目必须使用相同的量表级数`);
                            return false;
                        }

                        if (meanValue < 1 || meanValue > scaleLevel) {
                            alert(`配置验证失败：维度${i + 1}题目${j + 1}的目标均值应在1到${scaleLevel}之间（${scaleLevel}级量表），当前值：${meanValue}`);
                            return false;
                        }
                    }
                }
            }

            // 验证计分方向
            if (detail.scoringDirection && detail.scoringDirection !== 'positive' && detail.scoringDirection !== 'negative') {
                alert(`配置验证失败：维度${i + 1}题目${j + 1}的计分方向必须是'positive'或'negative'，当前值：${detail.scoringDirection}`);
                return false;
            }
        }

        // 验证维度题目数量
        if (dim.questionDetails.length < 2) {
            alert(`配置验证失败：维度${i + 1}至少需要2个题目，当前数量：${dim.questionDetails.length}`);
            return false;
        }

        if (dim.questionDetails.length > 20) {
            if (!confirm(`维度${i + 1}包含${dim.questionDetails.length}个题目，数量较多可能影响分析效果，是否继续导入？`)) {
                return false;
            }
        }
    }

    return true;
}

// 应用配置
function applyConfig(config) {
    // 清空现有维度
    const dimensionsList = document.getElementById('dimensionsList');
    dimensionsList.innerHTML = '';
    window.usedQuestions = new Set();

    // 应用全局参数
    document.getElementById('targetTotalAlpha').value = config.globalParams.targetTotalAlpha || 0.85;
    document.getElementById('targetKMO').value = config.globalParams.targetKMO || 0.7;
    document.getElementById('targetInterDimensionCorrelation').value = config.globalParams.targetInterDimensionCorrelation || 0.4;
    document.getElementById('tolerance').value = config.globalParams.tolerance || 0.02;

    // 应用维度配置
    config.dimensions.forEach((dimConfig, index) => {
        addDimension();

        const dimensionItem = document.querySelector(`[data-dimension-index="${index}"]`);
        if (!dimensionItem) return;

        // 设置维度名称
        const nameInput = dimensionItem.querySelector('.dimension-name');
        if (nameInput) {
            nameInput.value = dimConfig.name || `维度${index + 1}`;
        }

        // 设置目标信度
        const alphaInput = dimensionItem.querySelector('.dimension-alpha');
        if (alphaInput) {
            alphaInput.value = dimConfig.targetAlpha || 0.8;
        }

        // 选择题目和小题
        if (dimConfig.questionDetails) {
            // 新格式：使用questionDetails
            dimConfig.questionDetails.forEach(detail => {
                if (detail.type === 'question') {
                    // 普通题目
                    const checkbox = dimensionItem.querySelector(`[value="${detail.questionId}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        window.usedQuestions.add(detail.questionId);
                    }
                } else if (detail.type === 'subquestion') {
                    // 矩阵小题
                    const uniqueId = `${detail.mainQuestionId}_${detail.subIndex}`;
                    const checkbox = dimensionItem.querySelector(`[value="${uniqueId}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        window.usedQuestions.add(uniqueId);
                    }
                }
            });
        } else if (dimConfig.questions) {
            // 旧格式兼容：使用questions
            dimConfig.questions.forEach(questionId => {
                const checkbox = dimensionItem.querySelector(`[value="${questionId}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    window.usedQuestions.add(questionId);
                }
            });
        }

        // 更新选中题目显示
        updateSelectedQuestions(index);

        // 应用题目详细设置
        setTimeout(() => {
            if (dimConfig.questionDetails) {
                dimConfig.questionDetails.forEach(detail => {
                    let questionId;
                    if (detail.type === 'question') {
                        questionId = detail.questionId;
                    } else if (detail.type === 'subquestion') {
                        questionId = `${detail.mainQuestionId}_${detail.subIndex}`;
                    } else {
                        // 兼容旧格式
                        questionId = detail.questionId;
                    }

                    const scoringSwitch = dimensionItem.querySelector(`[data-question-id="${questionId}"].scoring-direction-switch`);
                    if (scoringSwitch) {
                        scoringSwitch.checked = (detail.scoringDirection || 'positive') === 'positive';
                        // 更新标签文本
                        const label = scoringSwitch.parentElement.querySelector('.scoring-direction-label');
                        if (label) {
                            label.textContent = scoringSwitch.checked ? '正向计分' : '反向计分';
                        }
                    }

                    const meanInput = dimensionItem.querySelector(`[data-question-id="${questionId}"].target-mean`);
                    if (meanInput && detail.targetMean !== null) {
                        meanInput.value = detail.targetMean;
                    }
                });
            }
        }, 100);
    });

    // 更新可选题目
    updateAvailableQuestions();
}



// 事件绑定
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        // 打开分析弹窗时加载结构
        document.getElementById('analysisBtn').addEventListener('click', async () => {
            await loadSurveyStructureForAnalysis();
            renderAnalysisParams();
        });
        // 切换分析类型时动态渲染参数
        document.getElementById('analysisType').addEventListener('change', renderAnalysisParams);
        // 点击分析按钮
        document.getElementById('runAnalysisBtn').addEventListener('click', runAnalysis);

        // 数据调整相关事件
        // 打开调整弹窗时加载结构
        document.getElementById('adjustmentBtn').addEventListener('click', async () => {
            await loadSurveyStructureForAdjustment();
            renderAdjustmentParams();
        });
        // 切换调整类型时动态渲染参数
        document.getElementById('adjustmentType').addEventListener('change', renderAdjustmentParams);
        // 点击调整按钮
        document.getElementById('runAdjustmentBtn').addEventListener('click', runAdjustment);
    });
}

// 工具函数：去除所有<del>和 标签但保留内容
function stripDelAndS(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    div.querySelectorAll('del, s').forEach(el => {
        el.replaceWith(document.createTextNode(el.textContent));
    });
    return div.innerHTML;
}

// 添加消息到聊天界面
function addMessage(role, content, messageId, time, messageData) {
    // 如果开头是.range-tag，前面插入可选中锚点
    if (content.startsWith('<span class="range-tag"')) {
        content = '<span class="select-anchor" contenteditable="false">&#8203;</span>' + content;
    }
    // 让所有.range-tag都带tabindex="0"
    content = content.replace(/<span class=\"range-tag\"/g, '<span class=\"range-tag\" tabindex="0"');
    // 在每个.range-tag前后都插入锚点
    content = content.replace(/(<span class=\"range-tag\" tabindex=\"0\"[^>]*>.*?<\/span>)/g, '<span class="select-anchor" contenteditable="false">&#8203;</span>$1<span class="select-anchor" contenteditable="false">&#8203;</span>');
    const messageList = document.getElementById('messageList');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role} chat-message`;
    if (messageId) messageDiv.setAttribute('data-message-id', messageId);

    // 渲染@@这是选中的数据范围([A-Z]+\d+(?::[A-Z]+\d+)?)@@为标签
    content = content.replace(/@@这是选中的数据范围([A-Z]+\d+(?::[A-Z]+\d+)?)@@/g, (match, range) => {
        return `<span class="range-tag" data-range="${range}">${range}</span>`;
    });
    // 只去掉包裹的"~~内容~~"，保留单独的"~"
    content = content.replace(/~~([^~]+)~~/g, '$1');
    // 去除所有 <del> 和   标签
    content = content.replace(/<del>(.*?)<\/del>/gi, '$1');
    content = content.replace(/ (.*?)<\/s>/gi, '$1');

    // 只对说明性文字部分做换行替换，表格部分不处理
    const splitIndex = content.indexOf('<div class="analysis-table-container"');
    if (splitIndex !== -1) {
        // 说明性文字部分
        let desc = content.substring(0, splitIndex);
        // 表格及后续部分
        let tables = content.substring(splitIndex);
        // 只对说明性文字做换行替换
        desc = desc.replace(/\r?\n/g, '<br>');
        content = desc + tables;
    } else {
        // 没有表格div，全部做换行替换
        content = content.replace(/\r?\n/g, '<br>');
    }

    // 直接赋值为HTML（只保留自定义range-tag标签，其它都按纯文本显示）
    messageDiv.innerHTML = content;

    // 给range-tag加点击高亮事件
    messageDiv.querySelectorAll('.range-tag').forEach(tag => {
        tag.onclick = function () {
            highlightRangeByText(tag.getAttribute('data-range'));
        };
    });

    // 显示时间
    if (time) {
        const timeDiv = document.createElement('div');
        timeDiv.className = 'msg-time';
        timeDiv.textContent = formatMsgTime(time);
        messageDiv.appendChild(timeDiv);
    }

    // 检查是否有配置文本，如果有则添加导出配置按钮
    if (messageData && messageData.configText && messageData.configText.trim() !== '') {
        const exportBtn = document.createElement('button');

        // 根据角色设置不同的样式类
        if (role === 'assistant') {
            exportBtn.className = 'export-config-btn export-config-btn-outside';
        } else {
            exportBtn.className = 'export-config-btn export-config-btn-inside';
        }

        exportBtn.innerHTML = '<i class="bi bi-upload"></i>';
        exportBtn.title = '导出配置到剪贴板';
        exportBtn.onclick = function(e) {
            e.stopPropagation();
            exportConfigToClipboard(messageData.configText);
        };

        // 将按钮添加到消息
        messageDiv.style.position = 'relative';
        messageDiv.appendChild(exportBtn);
    }

    // 为每个.range-tag前后插入锚点节点
    messageDiv.querySelectorAll('.range-tag').forEach(tag => {
        // 前面插入锚点
        const anchorBefore = document.createElement('span');
        anchorBefore.className = 'select-anchor';
        anchorBefore.setAttribute('contenteditable', 'false');
        anchorBefore.innerHTML = '&#8203;';
        tag.parentNode.insertBefore(anchorBefore, tag);
        // 后面插入锚点
        const anchorAfter = document.createElement('span');
        anchorAfter.className = 'select-anchor';
        anchorAfter.setAttribute('contenteditable', 'false');
        anchorAfter.innerHTML = '&#8203;';
        if (tag.nextSibling) {
            tag.parentNode.insertBefore(anchorAfter, tag.nextSibling);
        } else {
            tag.parentNode.appendChild(anchorAfter);
        }
    });

    messageList.appendChild(messageDiv);
    messageList.scrollTop = messageList.scrollHeight;
}

function formatMsgTime(time) {
    if (!time) return '';
    const date = new Date(time);
    if (isNaN(date.getTime())) return time;
    return date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0') + ' ' + String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0') + ':' + String(date.getSeconds()).padStart(2, '0');
}

// 初始化WebSocket连接
function initWebSocket() {
    // WebSocket连接已移除，当前版本不需要
    console.log('WebSocket连接已移除');
}

// 添加用户消息到界面
function addUserMessage(content) {
    addMessage('user', content);
}

// 添加助手消息到界面
function addAssistantMessage(content) {
    // 如果内容为空，显示占位内容
    addMessage('assistant', content && content.trim() ? content : 'AI正在思考...');
}

// 新的流式处理逻辑
async function handleStream(reader, onDone) {
    let decoder = new TextDecoder('utf-8');
    let buffer = '';
    let lastCompleteJson = '';
    let streamingContent = '';
    let accumulatedExplanation = '';

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        let lines = buffer.split('\n');
        buffer = lines.pop(); // 剩下的留给下次

        for (let line of lines) {
            line = line.trim();
            console.log('处理行:', line);
            
            if (line.startsWith('data:')) {
                // 移除所有data:前缀 - 使用更简单的方法
                let content = line;
                while (content.startsWith('data:')) {
                    content = content.substring(5);
                }
                content = content.trim();
                console.log('原始行:', line);
                console.log('移除data:前缀后:', content);
                if (content) {
                    console.log('收到数据片段:', content);
                    // 尝试解析JSON
                    try {
                        const jsonData = JSON.parse(content);
                        console.log('JSON解析成功:', jsonData);
                        lastCompleteJson = content;
                        
                        // 流式更新显示内容 - 直接拼接explanation内容
                        if (jsonData.explanation) {
                            console.log('处理explanation:', jsonData.explanation);
                            // 直接拼接，不做复杂处理
                            accumulatedExplanation += jsonData.explanation;
                            console.log('累积的explanation:', accumulatedExplanation);
                            updateAssistantMessageContent(accumulatedExplanation);
                        }
                        
                        // 如果有表格数据，实时更新
                        if (jsonData.tables && Array.isArray(jsonData.tables)) {
                            updateAssistantMessageWithTables(jsonData);
                        }
                        
                    } catch (e) {
                        // 如果解析失败，可能是部分数据，继续等待
                        console.log('部分数据，继续等待完整JSON:', content);
                        console.log('解析错误:', e.message);
                    }
                }
            } else if (line.startsWith('{')) {
                // 处理没有data:前缀的JSON行
                console.log('收到JSON片段:', line);
                try {
                    const jsonData = JSON.parse(line);
                    console.log('JSON解析成功:', jsonData);
                    lastCompleteJson = line;
                    
                    // 流式更新显示内容 - 直接拼接explanation内容
                    if (jsonData.explanation) {
                        console.log('处理explanation:', jsonData.explanation);
                        // 直接拼接，不做复杂处理
                        accumulatedExplanation += jsonData.explanation;
                        console.log('累积的explanation:', accumulatedExplanation);
                        updateAssistantMessageContent(accumulatedExplanation);
                    }
                    
                    // 如果有表格数据，实时更新
                    if (jsonData.tables && Array.isArray(jsonData.tables)) {
                        updateAssistantMessageWithTables(jsonData);
                    }
                    
                } catch (e) {
                    console.log('JSON解析失败:', line);
                    console.log('解析错误:', e.message);
                }
            }
        }
    }
    
    // 处理最后一行
    if (buffer.trim().startsWith('data:')) {
        let content = buffer.trim();
        while (content.startsWith('data:')) {
            content = content.substring(5);
        }
        content = content.trim();
        if (content) {
            try {
                const jsonData = JSON.parse(content);
                lastCompleteJson = content;
                if (jsonData.explanation) {
                    accumulatedExplanation += jsonData.explanation;
                    updateAssistantMessageContent(accumulatedExplanation);
                }
            } catch (e) {
                console.log('最后一行解析失败:', content);
            }
        }
    } else if (buffer.trim().startsWith('{')) {
        try {
            const jsonData = JSON.parse(buffer.trim());
            lastCompleteJson = buffer.trim();
            if (jsonData.explanation) {
                accumulatedExplanation += jsonData.explanation;
                updateAssistantMessageContent(accumulatedExplanation);
            }
        } catch (e) {
            console.log('最后一行JSON解析失败:', buffer.trim());
        }
    }

    // 流式输出完成后，使用完整的JSON数据进行最终处理
    if (lastCompleteJson) {
        try {
            const finalData = JSON.parse(lastCompleteJson);
            // 使用累积的explanation内容替换原始内容
            if (accumulatedExplanation) {
                finalData.explanation = accumulatedExplanation;
            }
            updateLastAssistantMessage(finalData);
            
            // 如果有表格变更，执行动画更新
            if (finalData.changedCells && Array.isArray(finalData.changedCells) && finalData.changedCells.length > 0) {
                updateTableDataWithAnimation(finalData.changedCells);
                // 数据修改后重新加载版本历史
                if (currentSessionId) {
                    setTimeout(() => {
                        loadVersionHistory();
                    }, 500);
                }
            }
        } catch (e) {
            console.error('最终JSON解析失败:', e);
            updateAssistantMessageContent('AI回复解析失败');
        }
    } else {
        updateAssistantMessageContent('AI暂无回复内容');
    }
    
    // 延迟重新加载消息列表，避免与动画冲突
    if (window.currentSessionId && typeof loadMessages === 'function') {
        setTimeout(() => {
            loadMessages(window.currentSessionId);
        }, 1000); // 延迟1秒，确保动画完成
    }

    // 流式处理完成后，只刷新版本历史，不重新加载会话数据（避免清除动画样式）
    if (currentSessionId) {
        setTimeout(() => {
            // 只重新加载版本历史，不重新加载整个会话数据
            loadVersionHistory();
        }, 600); // 延迟确保数据已保存
    }

    if (onDone) onDone();
}

// 重构sendMessage函数
async function sendMessage() {
    const input = document.getElementById('messageInput');
    let message = '';
    input.childNodes.forEach(node => {
        if (node.classList && node.classList.contains('range-tag')) {
            const rangeText = node.getAttribute('data-range');
            message += `@@这是选中的数据范围${rangeText}@@`;
        } else {
            message += node.textContent;
        }
    });
    if (!currentSessionId) {
        alert('请先选择或创建一个会话');
        return;
    }
    if (!message.trim()) {
        alert('请输入消息内容');
        return;
    }
    console.log('message', message);
    addUserMessage(message);
    input.innerHTML = '';
    setLoadingState(true, '思考中');
    addAssistantMessage('AI正在思考...');
    try {
        const response = await fetch('/ai-chat/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `sessionId=${encodeURIComponent(currentSessionId)}&message=${encodeURIComponent(message)}`
        });
        if (!response.body) throw new Error('流式响应不可用');
        const reader = response.body.getReader();
        await handleStream(
            reader,
            () => {
                setLoadingState(false);
                // 延迟刷新会话列表和代币余额，确保后端操作完成
                setTimeout(() => {
                    loadSessions();
                    updateTokenBalance(); // 更新代币余额
                }, 1500); // 延迟1.5秒确保数据库操作完成
            }
        );
    } catch (error) {
        setLoadingState(false);
        addMessage('system', 'AI助手连接失败: ' + error.message);
    }
}

// 实时更新助手消息内容（只做文本显示，不解析JSON）
function updateAssistantMessageContent(content) {
    const lastMessage = document.querySelector('.message.assistant:last-child');
    if (lastMessage) {
        // 处理换行符
        content = content.replace(/\r?\n/g, '<br>');
        // 直接设置innerHTML，不使用marked.parse
        lastMessage.innerHTML = content;
        // 滚动到底部
        scrollToBottom();
    }
}

// 新增：更新助手消息内容（包含表格）
function updateAssistantMessageWithTables(jsonData) {
    const lastMessage = document.querySelector('.message.assistant:last-child');
    if (lastMessage) {
        let html = '';
        if (jsonData.explanation) {
            html += `<div>${jsonData.explanation}</div>`;
        }
        if (jsonData.tables && Array.isArray(jsonData.tables)) {
            html += renderAnalysisTables(jsonData.tables);
        }
        lastMessage.innerHTML = html;
    }
}

// 设置加载状态
function setLoadingState(loading, status = '思考中') {
    const sendButton = document.querySelector('.chat-input .btn-primary');
    const input = document.getElementById('messageInput');

    if (sendButton && input) {
        if (loading) {
            sendButton.disabled = true;
            input.disabled = true;
            sendButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> AI${status}...`;
        } else {
            sendButton.disabled = false;
            input.disabled = false;
            sendButton.innerHTML = '发送';
            input.focus();
        }
    }
}

// 检查是否处于加载状态
function isLoading() {
    const sendButton = document.querySelector('.chat-input .btn-primary');
    return sendButton && sendButton.disabled;
}

// 辅助函数：递归剥离多余data:包裹，直到能被JSON.parse
function extractValidJson(str) {
    // 提取所有 data: 开头的行
    const matches = str.match(/data:(.*?)(?=data:|$)/gs);
    if (matches) {
        // 从后往前找第一个能被JSON.parse的
        for (let i = matches.length - 1; i >= 0; i--) {
            let jsonStr = matches[i].replace(/^data:/, '').trim();
            try {
                return JSON.parse(jsonStr);
            } catch (e) {
                // 跳过
            }
        }
    }
    // 如果没有data:，尝试整体解析
    try {
        return JSON.parse(str);
    } catch (e) {
        return null;
    }
}

function updateLastAssistantMessage(content) {
    try {
        let data = typeof content === 'string' ? JSON.parse(content) : content;
        let html = '';
        if (data.explanation) {
            html += `<div>${data.explanation}</div>`;
        }
        if (data.tables && Array.isArray(data.tables)) {
            html += renderAnalysisTables(data.tables);
        }
        const lastMessage = document.querySelector('.message.assistant:last-child');
        if (lastMessage) {
            lastMessage.innerHTML = html;
        }
        // 注释掉重复的动画更新调用，避免与流式输出完成后的调用冲突
        // 动画更新已在流式输出完成后统一处理
        // if (data.changedCells && Array.isArray(data.changedCells) && data.changedCells.length > 0) {
        //     updateTableDataWithAnimation(data.changedCells);
        //     // 数据修改后重新加载版本历史
        //     if (currentSessionId) {
        //         setTimeout(() => {
        //             loadVersionHistory();
        //         }, 500); // 延迟500ms确保数据更新完成
        //     }
        // }
        scrollToBottom();
    } catch (e) {
        console.error('Error updating message:', e);
        updateAssistantMessageContent('消息解析失败');
    }
}

// 带动画效果更新表格数据
function updateTableDataWithAnimation(modifications) {
    console.log('updateTableDataWithAnimation', modifications);
    console.log('hot', modifications);
    if (!hot) return;

    // 1. 计算需要的最大行和最大列
    let maxRow = 0, maxCol = 0;
    modifications.forEach(mod => {
        if (mod[0] > maxRow) maxRow = mod[0];
        if (mod[1] > maxCol) maxCol = mod[1];
    });

    // 2. 一次性插入所有需要的行和列
    let sourceData = hot.getSourceData();
    if (maxRow >= sourceData.length) {
        hot.alter('insert_row', sourceData.length, maxRow - sourceData.length + 1);
    }
    sourceData = hot.getSourceData();
    if (sourceData.length > 0 && maxCol >= sourceData[0].length) {
        hot.alter('insert_col', sourceData[0].length, maxCol - sourceData[0].length + 1);
    }

    // 3. 创建一个Promise链来处理所有修改（动画效果）
    let promise = Promise.resolve();
    modifications.forEach((mod, idx) => {
        promise = promise.then(() => {
            return new Promise(resolve => {
                // 获取最新的数据源以确保索引验证正确
                const currentData = hot.getSourceData();

                // 判断索引是否有效
                if (mod[0] >= 0 && mod[1] >= 0 &&
                    mod[0] < currentData.length &&
                    currentData[mod[0]] && mod[1] < currentData[mod[0]].length) {
                    // 更新数据
                    console.log(`[表格赋值] setDataAtCell(${mod[0]}, ${mod[1]}, ${mod[2]})`);
                    hot.setDataAtCell(mod[0], mod[1], mod[2]);

                    // 滚动到被修改单元格并居中
                    hot.scrollViewportTo(mod[0], mod[1], false, false);

                    // 同步更新数据源（使用最新的数据源）
                    const updatedData = hot.getSourceData();
                    if (Array.isArray(updatedData[mod[0]])) {
                        updatedData[mod[0]][mod[1]] = mod[2];
                    }

                    // 添加动画class
                    hot.setCellMeta(mod[0], mod[1], 'className', 'scanning cell-modified');
                    hot.render();

                    // 150ms后移除动画class，只保留修改标记
                    setTimeout(() => {
                        // 确保单元格仍然存在再设置样式
                        try {
                            hot.setCellMeta(mod[0], mod[1], 'className', 'cell-modified');
                            hot.render();
                        } catch (e) {
                            console.warn('设置单元格样式失败:', e);
                        }
                        resolve();
                    }, 150);
                } else {
                    console.warn(`Invalid cell modification: row=${mod[0]}, col=${mod[1]}`);
                    resolve();
                }
            });
        });
    });

    // 所有修改完成后的处理
    promise.then(() => {
        console.log('所有单元格动画修改完成');

        // 删除所有全为空的行（不含表头）
        if (hot) {
            let data = hot.getSourceData();
            for (let i = data.length - 1; i >= 0; i--) {
                if (Array.isArray(data[i]) && data[i].every(cell => cell === null || cell === undefined || cell === '')) {
                    hot.alter('remove_row', i);
                }
            }
        }
        // 防止重复显示"数据调整完成"
        const messageList = document.getElementById('messageList');
        const lastSystemMsg = Array.from(messageList.querySelectorAll('.message.system')).pop();
        if (!lastSystemMsg || lastSystemMsg.textContent.trim() !== '数据调整完成') {
            addMessage('system', '数据调整完成');
        }
        
        // 数据修改完成后重新加载版本历史
        if (currentSessionId) {
            loadVersionHistory();
            // 新增：数据变更后，自动选中第一个（当前）版本，并触发change事件
            setTimeout(() => {
                const versionSelect = document.getElementById('versionSelect');
                if (versionSelect && versionSelect.options.length > 0) {
                    versionSelect.selectedIndex = 0; // 选中第一个（当前）版本
                    // 标记为程序化变更，防止change事件里重复刷新
                    versionSelect.dataset.programmaticChange = 'true';
                    versionSelect.dispatchEvent(new Event('change'));
                }
            }, 300); // 延迟保证loadVersionHistory已完成
        }
    }).catch(error => {
        console.error('更新表格数据失败:', error);
        addMessage('system', '数据调整失败: ' + error.message);
    });
}


// 获取指定版本与其上一个版本相比发生变化的单元格位置和值
async function getChangedCellsBetweenVersions(sessionId, currentVersionId = null) {
    try {
        const response = await fetch(`/ai-chat/excel/versions?sessionId=${sessionId}`);
        const data = await response.json();
        const versions = Array.isArray(data.data) ? data.data : [];

        if (versions.length < 2) {
            // 如果只有一个版本或没有版本，则没有变化
            return { changedCells: new Set(), cellChanges: new Map() };
        }

        let currentVersion, previousVersion;

        if (currentVersionId) {
            // 查找指定版本和其上一个版本
            const currentIndex = versions.findIndex(v => String(v.id) === String(currentVersionId));
            if (currentIndex === -1 || currentIndex === versions.length - 1) {
                // 如果找不到版本或者是最后一个版本（初始版本），则没有变化
                return { changedCells: new Set(), cellChanges: new Map() };
            }
            currentVersion = versions[currentIndex];
            previousVersion = versions[currentIndex + 1];
        } else {
            // 默认比较最新版本和上一个版本
            currentVersion = versions[0];
            previousVersion = versions[1];
        }

        // 获取两个版本的Excel数据
        const currentDataResponse = await fetch(`/ai-chat/excel/version/${currentVersion.id}?sessionId=${sessionId}`);
        const currentDataResult = await currentDataResponse.json();
        const currentData = JSON.parse(currentDataResult.excelData || '[]');

        const previousDataResponse = await fetch(`/ai-chat/excel/version/${previousVersion.id}?sessionId=${sessionId}`);
        const previousDataResult = await previousDataResponse.json();
        const previousData = JSON.parse(previousDataResult.excelData || '[]');

        // 比较两个版本的数据，找出差异
        const changedCells = new Set();
        const cellChanges = new Map(); // 存储每个单元格的变化信息
        const maxRows = Math.max(currentData.length, previousData.length);

        for (let row = 0; row < maxRows; row++) {
            const currentRow = currentData[row] || [];
            const previousRow = previousData[row] || [];
            const maxCols = Math.max(currentRow.length, previousRow.length);

            for (let col = 0; col < maxCols; col++) {
                const currentCell = currentRow[col] || '';
                const previousCell = previousRow[col] || '';

                if (currentCell !== previousCell) {
                    const cellKey = `${row},${col}`;
                    changedCells.add(cellKey);

                    // 计算单元格坐标（如A1, B2等）
                    const cellCoordinate = getCellCoordinate(row, col);

                    cellChanges.set(cellKey, {
                        previousValue: previousCell,
                        currentValue: currentCell,
                        row: row,
                        col: col,
                        coordinate: cellCoordinate
                    });
                }
            }
        }

        return { changedCells, cellChanges };
    } catch (error) {
        console.error('获取版本差异数据失败:', error);
        return { changedCells: new Set(), cellChanges: new Map() };
    }
}

// 将行列索引转换为Excel坐标（如A1, B2等）
function getCellCoordinate(row, col) {
    let columnName = '';
    let colIndex = col + 1; // Excel列从1开始，而不是0

    // 转换列索引为字母（A=1, B=2, ..., Z=26, AA=27, AB=28, ...）
    while (colIndex > 0) {
        colIndex--; // 调整为0基索引
        columnName = String.fromCharCode((colIndex % 26) + 65) + columnName;
        colIndex = Math.floor(colIndex / 26);
    }

    // 行号从1开始
    return columnName + (row + 1);
}

// 兼容性函数，保持原有调用方式
async function getChangedCellsFromPreviousVersion(sessionId) {
    return await getChangedCellsBetweenVersions(sessionId);
}

// 全局变量存储单元格变化信息，用于悬浮提示
let globalCellChanges = new Map();
let currentTooltip = null;

// 为修改过的单元格设置悬浮提示（现在主要用于清理旧的事件监听器）
function setupCellTooltips() {
    // 移除之前手动绑定的事件监听器（如果有的话）
    removeExistingTooltips();

    // 现在悬浮提示通过 Handsontable 的内置事件处理，不需要手动绑定
}

// 移除现有的提示
function removeExistingTooltips() {
    // 移除所有现有的自定义tooltip
    document.querySelectorAll('.cell-tooltip').forEach(tooltip => tooltip.remove());

    // 移除所有单元格上的事件监听器
    if (hot) {
        const rowCount = hot.countRows();
        const colCount = hot.countCols();
        for (let row = 0; row < rowCount; row++) {
            for (let col = 0; col < colCount; col++) {
                const cellElement = hot.getCell(row, col);
                if (cellElement && cellElement._tooltipMouseEnter) {
                    cellElement.removeEventListener('mouseenter', cellElement._tooltipMouseEnter);
                    cellElement.removeEventListener('mouseleave', cellElement._tooltipMouseLeave);
                    delete cellElement._tooltipMouseEnter;
                    delete cellElement._tooltipMouseLeave;
                    cellElement.removeAttribute('title');
                }
            }
        }
    }
}

// 为当前版本绑定悬浮提示事件（使用已加载的差异数据）
function bindTooltipEventsForCurrentVersion() {
    // 现在不需要手动绑定事件，数据已经存储在 globalCellChanges 中
    // Handsontable 的内置事件会自动处理悬浮提示
}

// 为指定版本绑定悬浮提示事件
async function bindTooltipEventsForVersion(versionId) {
    if (!currentSessionId || !versionId) return;

    // 移除之前的提示
    removeExistingTooltips();

    // 检查是否是初始版本（ID以initial_开头）
    if (String(versionId).startsWith('initial_')) {
        // 初始版本没有上一个版本，清空差异数据
        globalCellChanges = new Map();
        return;
    }

    // 获取当前版本与上一版本的差异
    const result = await getChangedCellsBetweenVersions(currentSessionId, versionId);
    const { changedCells, cellChanges } = result;

    // 存储到全局变量，供 Handsontable 内置事件使用
    globalCellChanges = cellChanges;
}

// 添加自定义悬浮提示
function addCustomTooltip(cellElement, changeInfo) {
    let tooltip = null;

    // 移除之前可能存在的事件监听器
    cellElement.removeEventListener('mouseenter', cellElement._tooltipMouseEnter);
    cellElement.removeEventListener('mouseleave', cellElement._tooltipMouseLeave);

    const mouseEnterHandler = function() {
        // 创建tooltip元素
        tooltip = document.createElement('div');
        tooltip.className = 'cell-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <div class="tooltip-title">单元格修改历史 (${changeInfo.coordinate})</div>
                <div class="tooltip-change">
                    <span class="tooltip-label">修改前:</span>
                    <span class="tooltip-old-value">"${changeInfo.previousValue || '(空)'}"</span>
                </div>
                <div class="tooltip-change">
                    <span class="tooltip-label">修改后:</span>
                    <span class="tooltip-new-value">"${changeInfo.currentValue || '(空)'}"</span>
                </div>
            </div>
        `;

        // 设置tooltip位置
        document.body.appendChild(tooltip);

        const rect = cellElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        let top = rect.top - tooltipRect.height - 8;

        // 确保tooltip不会超出屏幕边界
        if (left < 10) left = 10;
        if (left + tooltipRect.width > window.innerWidth - 10) {
            left = window.innerWidth - tooltipRect.width - 10;
        }
        if (top < 10) {
            top = rect.bottom + 8; // 如果上方空间不够，显示在下方
            tooltip.classList.add('bottom'); // 添加底部显示的样式类
        }

        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
        tooltip.style.opacity = '1';
    };

    const mouseLeaveHandler = function() {
        if (tooltip) {
            tooltip.remove();
            tooltip = null;
        }
    };

    // 存储事件处理器引用，以便后续移除
    cellElement._tooltipMouseEnter = mouseEnterHandler;
    cellElement._tooltipMouseLeave = mouseLeaveHandler;

    cellElement.addEventListener('mouseenter', mouseEnterHandler);
    cellElement.addEventListener('mouseleave', mouseLeaveHandler);
}

// 显示单元格悬浮提示（使用Handsontable内置事件）
function showCellTooltip(row, col, cellElement, event) {
    // 检查当前是否在预览初始版本
    const versionSelect = document.getElementById('versionSelect');
    if (versionSelect && versionSelect.value && String(versionSelect.value).startsWith('initial_')) {
        return; // 初始版本不显示悬浮提示
    }

    // 检查该单元格是否有变化信息
    const cellKey = `${row},${col}`;
    const changeInfo = globalCellChanges.get(cellKey);

    if (!changeInfo) {
        return; // 没有变化信息，不显示提示
    }

    // 隐藏之前的提示
    hideCellTooltip();

    // 创建新的提示
    currentTooltip = document.createElement('div');
    currentTooltip.className = 'cell-tooltip';
    currentTooltip.innerHTML = `
        <div class="tooltip-content">
            <div class="tooltip-title">单元格修改历史 (${changeInfo.coordinate})</div>
            <div class="tooltip-change">
                <span class="tooltip-label">修改前:</span>
                <span class="tooltip-old-value">"${changeInfo.previousValue || '(空)'}"</span>
            </div>
            <div class="tooltip-change">
                <span class="tooltip-label">修改后:</span>
                <span class="tooltip-new-value">"${changeInfo.currentValue || '(空)'}"</span>
            </div>
        </div>
    `;

    // 设置tooltip位置
    document.body.appendChild(currentTooltip);

    const rect = cellElement.getBoundingClientRect();
    const tooltipRect = currentTooltip.getBoundingClientRect();

    let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
    let top = rect.top - tooltipRect.height - 8;

    // 确保tooltip不会超出屏幕边界
    if (left < 10) left = 10;
    if (left + tooltipRect.width > window.innerWidth - 10) {
        left = window.innerWidth - tooltipRect.width - 10;
    }
    if (top < 10) {
        top = rect.bottom + 8; // 如果上方空间不够，显示在下方
        currentTooltip.classList.add('bottom');
    }

    currentTooltip.style.left = left + 'px';
    currentTooltip.style.top = top + 'px';
    currentTooltip.style.opacity = '1';
}

// 隐藏单元格悬浮提示
function hideCellTooltip() {
    if (currentTooltip) {
        currentTooltip.remove();
        currentTooltip = null;
    }
}

// 重构refreshTableFromSession，只负责渲染，不再fetch
function refreshTableFromSession(session) {
    if (session && session.excelData) {
        const data = JSON.parse(session.excelData);
        if (hot) {
            hot.updateSettings({
                colHeaders: true, // 自动ABC
                rowHeaders: true  // 自动123
            });
            hot.loadData(data);
            hot.render();

            // 清除所有单元格的修改样式
            const rowCount = hot.countRows();
            const colCount = hot.countCols();
            for (let row = 0; row < rowCount; row++) {
                for (let col = 0; col < colCount; col++) {
                    hot.setCellMeta(row, col, 'className', '');
                }
            }

            // 异步加载与上一版本的差异标记（仅对当前版本）
            if (currentSessionId) {
                getChangedCellsFromPreviousVersion(currentSessionId).then(result => {
                    const { changedCells, cellChanges } = result;

                    // 存储到全局变量，供悬浮提示使用
                    globalCellChanges = cellChanges;

                    changedCells.forEach(cellKey => {
                        const [row, col] = cellKey.split(',').map(Number);
                        if (row < rowCount && col < colCount) {
                            hot.setCellMeta(row, col, 'className', 'cell-modified');
                        }
                    });
                    hot.render();

                    // 为当前版本绑定悬浮事件（显示与上一版本的差异）
                    bindTooltipEventsForCurrentVersion();
                });
            }

            hot.render();
        }
        
        // 确保表格容器回到正常状态（移除预览相关的CSS类）
        const excelContainer = document.querySelector('.excel-container');
        if (excelContainer) {
            excelContainer.classList.remove('preview-version');
        }
        
        // 隐藏"切换为目前版本"按钮
        const switchToCurrentBtn = document.getElementById('switchToCurrentBtn');
        if (switchToCurrentBtn) {
            switchToCurrentBtn.style.display = 'none';
        }
        
        // 隐藏预览提示
        hidePreviewTip();
    }
}

// 修改loadSessionData，确保版本历史加载
function loadSessionData(sessionId) {
    console.log('Loading session data for:', sessionId);
    if (!sessionId) {
        console.error('No session ID provided');
        return;
    }

    // 先清空版本选择框
    const versionSelect = document.getElementById('versionSelect');
    versionSelect.innerHTML = '<option value="">加载中...</option>';
    versionSelect.disabled = true;

    fetch(`/ai-chat/session/uuid/${sessionId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(session => {
            console.log('Received session data:', session);
            document.getElementById('uploadContainer').style.display = 'none';
            document.getElementById('excelTable').style.display = 'block';
            document.getElementById('toolbar').style.display = 'flex';
            document.getElementById('exportBtn').style.display = 'inline-flex';
            document.getElementById('fullscreenBtn').style.display = 'inline-flex';

            const surveyLinkShortElement = document.getElementById('surveyLinkShort');
            const surveyLinkContainer = document.getElementById('surveyLinkContainer');
            surveyLinkShortElement.textContent = extractSurveyLinkShort(session.surveyLink);
            surveyLinkShortElement.href = session.surveyLink;
            surveyLinkContainer.style.display = 'inline';
            document.getElementById('sessionCostCard').style.display = 'inline';

            // 显示版本控制模块
            const versionControl = document.querySelector('.version-control');
            if (versionControl) {
                versionControl.style.display = 'flex';
            }

            document.getElementById('currentSessionTokenConsumed').textContent = session.tokenConsumed || 0;

            // 渲染表格
            refreshTableFromSession(session);

            // 显示切换按钮（只有在有数据时才显示）
            if (session.excelData) {
                showToggleAnswerButton();
            } else {
                hideToggleAnswerButton();
            }

            // 加载消息和上下文
            loadMessages(session.uuid);

            // 直接加载版本历史，不使用setTimeout
            console.log('Loading version history after session data loaded');
            loadVersionHistory(true); // 强制选择当前版本

            // 修正：每次进入会话都刷新退出登录按钮状态
            updateLogoutHeaderBtn();
            updateLogoutToolbarBtn();
        })
        .catch(error => {
            console.error('加载会话数据失败:', error);
            alert('加载会话数据失败: ' + error.message);
            versionSelect.innerHTML = '<option value="">加载失败</option>';
            versionSelect.disabled = false;
            // 隐藏版本控制模块
            const versionControl = document.querySelector('.version-control');
            if (versionControl) {
                versionControl.style.display = 'none';
            }
            // 兜底：刷新退出登录按钮状态
            updateLogoutHeaderBtn();
            updateLogoutToolbarBtn();
        });
}

// 加载消息
function loadMessages(sessionId) {
    fetch(`/ai-chat/session/uuid/${sessionId}/messages`)
        .then(response => response.json())
        .then(messages => {
            const messageList = document.getElementById('messageList');
            messageList.innerHTML = ''; // 清空现有消息
            messages.forEach(message => {
                let content = message.content;

                // 如果有表格数据，解析并添加表格
                if (message.tableData) {
                    try {
                        const tables = JSON.parse(message.tableData);
                        content += renderAnalysisTables(tables);
                    } catch (e) {
                        console.error('Error parsing table data:', e);
                    }
                }

                addMessage(message.role, content, message.id, message.createTime, message);
            });
            messageList.scrollTop = messageList.scrollHeight;
        })
        .catch(error => {
            console.error('加载消息历史失败:', error);
        });
}





// 格式化分析结果
function formatAnalysisResult(result) {
    let html = '<div class="analysis-content">';
    if (result.success) {
        html += `<div class="alert alert-success">分析成功</div>`;
        html += `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
    } else {
        html += `<div class="alert alert-danger">分析失败: ${result.message}</div>`;
    }
    html += '</div>';
    return html;
}

// 导出Excel
function exportExcel() {
    if (!currentSessionId) {
        alert('请先选择或创建一个会话');
        return;
    }
    const data = hot.getData();

    // 从allSessions数组中查找当前会话对象来获取名称
    const currentSession = allSessions.find(session => session.uuid === currentSessionId);
    const sessionName = currentSession ? currentSession.sessionName.replace(/[\/\\:*?"<>|]/g, '') : '会话'; // 移除非法字符

    // 生成规范的时间格式（YYYYMMDDHHmmss）
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // 月份是 0-11
    const day = now.getDate();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    const timeString = `${year}年${month}月${day}日${hours}时${minutes}分${seconds}秒`;

    // 构建文件名
    const filename = `${sessionName}_${timeString}.xlsx`;
    console.log('Generated filename:', filename);

    fetch(`/ai-chat/session/uuid/${currentSessionId}/export`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`导出失败: ${text}`);
                });
            }

            // 验证内容类型是否正确
            const contentType = response.headers.get('Content-Type');
            if (!contentType.includes('spreadsheetml')) {
                throw new Error('服务器返回了无效的文件类型');
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;

            // 添加正确的MIME类型（虽然通常由blob类型决定）
            a.type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

            document.body.appendChild(a);
            a.click();

            // 添加延迟确保下载完成
            setTimeout(() => {
                window.URL.revokeObjectURL(url);
                a.remove();
            }, 100);
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert(error.message);
        });
}

// 重命名会话
function renameSession(uuid) {
    const newName = prompt('请输入新的会话名称：');
    if (!newName) return;

    fetch(`/ai-chat/session/uuid/${uuid}/rename?newName=${encodeURIComponent(newName)}`, {
        method: 'PUT'
    })
        .then(response => {
            if (response.ok) {
                loadSessions();
            } else {
                throw new Error('重命名失败');
            }
        })
        .catch(error => {
            console.error('重命名会话失败:', error);
            alert('重命名会话失败');
        });
}

// 删除会话
function deleteSession(uuid) {
    if (!confirm('确定要删除这个会话吗？')) return;

    fetch(`/ai-chat/session/uuid/${uuid}`, {
        method: 'DELETE'
    })
        .then(response => {
            if (response.ok) {
                if (currentSessionId === uuid) {
                    createNewChat();
                }
                loadSessions();
            } else {
                throw new Error('删除失败');
            }
        })
        .catch(error => {
            console.error('删除会话失败:', error);
            alert('删除会话失败');
        });
}

// 提取问卷链接简写
function extractSurveyLinkShort(link) {
    try {
        const url = new URL(link);
        const pathname = url.pathname;
        const parts = pathname.split('/');
        const vmIndex = parts.indexOf('vm');
        if (vmIndex !== -1 && vmIndex + 1 < parts.length) {
            const filename = parts[vmIndex + 1];
            const aspxIndex = filename.lastIndexOf('.aspx');
            if (aspxIndex !== -1) {
                return filename.substring(0, aspxIndex);
            }
        }
    } catch (e) {
        console.error('提取问卷链接简写失败:', e);
    }
    return link; // 提取失败则返回原链接
}

// 添加全屏预览功能
function toggleFullscreen() {
    const container = document.querySelector('.excel-container');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const icon = fullscreenBtn.querySelector('i');
    if (!container.classList.contains('fullscreen')) {
        // 进入页面内全屏
        container.classList.add('fullscreen');
        icon.classList.remove('bi-arrows-fullscreen');
        icon.classList.add('bi-arrows-angle-contract');
        fullscreenBtn.innerHTML = '<i class="bi bi-arrows-angle-contract"></i> 退出全屏';
        // 显示全屏操作按钮
        const fullscreenToggleBtn = document.getElementById('fullscreenToggleAnswerBtn');
        if (fullscreenToggleBtn) fullscreenToggleBtn.style.display = 'inline-flex';
    } else {
        // 退出页面内全屏
        container.classList.remove('fullscreen');
        icon.classList.remove('bi-arrows-angle-contract');
        icon.classList.add('bi-arrows-fullscreen');
        fullscreenBtn.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 全屏预览';
        // 隐藏全屏操作按钮
        const fullscreenToggleBtn = document.getElementById('fullscreenToggleAnswerBtn');
        if (fullscreenToggleBtn) fullscreenToggleBtn.style.display = 'none';
    }
    // 重新渲染表格以适应新的容器大小
    if (typeof hot !== 'undefined' && hot) {
        setTimeout(() => {
            hot.render();
            if (typeof hot.refreshDimensions === 'function') {
                hot.refreshDimensions();
            }
        }, 300);
    }
}
// 监听全屏变化，自动切换按钮状态
if (typeof window !== 'undefined') {
    document.addEventListener('fullscreenchange', function () {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        const icon = fullscreenBtn ? fullscreenBtn.querySelector('i') : null;
        if (!document.fullscreenElement) {
            // 退出全屏
            if (icon) {
                icon.classList.remove('bi-arrows-angle-contract');
                icon.classList.add('bi-arrows-fullscreen');
                fullscreenBtn.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 全屏预览';
            }
            const fullscreenToggleBtn = document.getElementById('fullscreenToggleAnswerBtn');
            if (fullscreenToggleBtn) {
                fullscreenToggleBtn.style.display = 'none';
            }
        }
    });
}

// 缩放比例
let currentZoom = 1.0;
const zoomStep = 0.1;
const minZoom = 0.5;
const maxZoom = 2.0;

// 放大表格
function zoomInTable() {
    if (!hot) return;
    if (currentZoom < maxZoom) {
        currentZoom += zoomStep;
        updateTableZoom();
    }
}

// 缩小表格
function zoomOutTable() {
    if (!hot) return;
    if (currentZoom > minZoom) {
        currentZoom -= zoomStep;
        updateTableZoom();
    }
}

// 更新表格缩放
function updateTableZoom() {
    if (!hot) return;
    const defaultColWidth = 100; // 默认列宽
    const defaultRowHeight = 23; // 默认行高 (Handsontable默认行高约为23px)

    const newColWidth = defaultColWidth * currentZoom;
    const newRowHeight = defaultRowHeight * currentZoom;

    // 更新列宽和行高
    hot.updateSettings({
        colWidths: newColWidth,
        rowHeights: newRowHeight
    });

    hot.render(); // 重新渲染表格
}

// 配置marked选项
marked.setOptions({
    highlight: function (code, lang) {
        if (lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        }
        return hljs.highlightAuto(code).value;
    },
    breaks: true,
    gfm: true
});

// 添加回车发送事件监听
document.getElementById('messageInput').addEventListener('keypress', function (event) {
    // 检查是否按下了Enter键，并且没有按下Shift键
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault(); // 阻止默认的换行行为
        sendMessage();
    }
});

// 初始化页面
function initPage() {
    // 初始隐藏工具栏
    document.getElementById('toolbar').style.display = 'none';
    document.getElementById('exportBtn').style.display = 'none';
    document.getElementById('fullscreenBtn').style.display = 'none';
    
    // 初始隐藏切换按钮
    hideToggleAnswerButton();

    loadSessions();
    initHandsontable();
    setupDragAndDrop();
    initWebSocket();
    setupInputDeleteTag();

    // 添加搜索输入框事件监听
    document.getElementById('sessionSearch').addEventListener('input', function () {
        const searchTerm = this.value.toLowerCase();
        const filteredSessions = allSessions.filter(session => {
            const sessionNameMatch = session.sessionName && session.sessionName.toLowerCase().includes(searchTerm);
            const surveyLinkMatch = session.surveyLink && session.surveyLink.toLowerCase().includes(searchTerm);
            return sessionNameMatch || surveyLinkMatch;
        });
        renderSessions(filteredSessions);
    });

    // 绑定会话列表点击事件
    document.getElementById('sessionList').addEventListener('click', function (e) {
        const item = e.target.closest('.session-item');
        if (item) {
            const uuid = item.getAttribute('data-uuid');
            if (uuid) {
                selectSession(uuid);
            }
        }
    });

    // 添加版本选择事件监听
    document.getElementById('versionSelect').addEventListener('change', updateVersionButtons);
}

// 页面加载时自动调用
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', initPage);
}

// 设置拖放上传
function setupDragAndDrop() {
    const container = document.getElementById('uploadContainer');

    container.addEventListener('dragover', (e) => {
        e.preventDefault();
        container.classList.add('dragover');
    });

    container.addEventListener('dragleave', () => {
        container.classList.remove('dragover');
    });

    container.addEventListener('drop', (e) => {
        e.preventDefault();
        container.classList.remove('dragover');
        const file = e.dataTransfer.files[0];
        if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
            document.getElementById('excelFile').files = e.dataTransfer.files;
        }
    });
}

// 更新代币余额和当前会话消费
function updateTokenBalance() {
    if (!currentTokenCode) {
        console.log('No token code available for balance update');
        return;
    }

    fetch('/token/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ tokenCode: currentTokenCode })
    })
    .then(response => response.json())
    .then(data => {
        if (data.valid) {
            document.getElementById('tokenCount').textContent = data.tokenCount;
            document.getElementById('totalTokenConsumed').textContent = data.totalTokensConsumed || 0;
            console.log('Token balance updated:', data.tokenCount);

            // 同时更新当前会话的消费统计
            if (currentSessionId) {
                updateCurrentSessionTokenConsumed();
            }
        } else {
            console.warn('Token validation failed during balance update');
        }
    })
    .catch(error => {
        console.error('更新代币余额失败:', error);
    });
}

// 更新当前会话的代币消费
function updateCurrentSessionTokenConsumed() {
    if (!currentSessionId) {
        return;
    }

    fetch(`/ai-chat/session/uuid/${currentSessionId}`)
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to fetch session data');
        }
        return response.json();
    })
    .then(session => {
        document.getElementById('currentSessionTokenConsumed').textContent = session.tokenConsumed || 0;
        console.log('Current session token consumed updated:', session.tokenConsumed);
    })
    .catch(error => {
        console.error('更新当前会话代币消费失败:', error);
    });
}

// 验证代币
function validateToken() {
    const tokenCode = document.getElementById('tokenCode').value.trim();

    if (!tokenCode) {
        alert('请输入代币码');
        updateLogoutHeaderBtn();
        updateLogoutToolbarBtn();
        return;
    }

    // 显示加载弹框
    showGlobalLoading('验证代币中', '正在验证您的代币，请稍候...');

    fetch('/token/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ tokenCode: tokenCode })
    })
        .then(response => response.json())
        .then(data => {
            hideGlobalLoading();

            if (data.valid) {
                // 显示成功提示
                showToast('代币验证成功！', 'success');

                currentTokenCode = tokenCode;
                document.getElementById('tokenInputSection').style.display = 'none';
                document.getElementById('uploadSection').style.display = 'block';
                document.getElementById('tokenCount').textContent = data.tokenCount;
                document.getElementById('totalTokenConsumed').textContent = data.totalTokensConsumed || 0;
                updateLogoutHeaderBtn();
                updateLogoutToolbarBtn();
                const maskedTokenCode = tokenCode.substring(0, 2) + '****' + tokenCode.substring(tokenCode.length - 2);
                document.getElementById('displayTokenCode').textContent = maskedTokenCode;
                loadSessions();
            } else {
                showToast('无效的代币码或代币余额不足', 'error');
                currentTokenCode = null;
                updateLogoutHeaderBtn();
                updateLogoutToolbarBtn();
            }
        })
        .catch(error => {
            hideGlobalLoading();
            console.error('验证代币失败:', error);
            showToast('验证失败：' + error.message, 'error');
            currentTokenCode = null;
            updateLogoutHeaderBtn();
            updateLogoutToolbarBtn();
        });
}

// 修改uploadAndParse，fetch后直接传session给refreshTableFromSession
function uploadAndParse() {
    const surveyLink = document.getElementById('surveyLink').value;
    const excelFile = document.getElementById('excelFile').files[0];

    if (!surveyLink || !excelFile || !currentTokenCode) {
        showToast('请填写问卷链接并选择Excel文件', 'error');
        return;
    }

    // 显示加载弹框
    showGlobalLoading('创建会话中', '正在上传文件并创建会话，请稍候...');

    const formData = new FormData();
    formData.append('surveyLink', surveyLink);
    formData.append('excelFile', excelFile);
    formData.append('tokenCode', currentTokenCode);

    fetch('/ai-chat/session', {
        method: 'POST',
        body: formData
    })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(text || '创建会话失败');
                });
            }
            return response.json();
        })
        .then(session => {
            currentSessionId = session.uuid;
            document.getElementById('uploadContainer').style.display = 'none';
            document.getElementById('excelTable').style.display = 'block';
            document.getElementById('toolbar').style.display = 'flex';
            document.getElementById('exportBtn').style.display = 'inline-flex';
            document.getElementById('fullscreenBtn').style.display = 'inline-flex';
            const surveyLinkShortElement = document.getElementById('surveyLinkShort');
            const surveyLinkContainer = document.getElementById('surveyLinkContainer');
            surveyLinkShortElement.textContent = extractSurveyLinkShort(session.surveyLink);
            surveyLinkShortElement.href = session.surveyLink;
            surveyLinkContainer.style.display = 'inline';
            document.getElementById('sessionCostCard').style.display = 'inline';
            document.getElementById('currentSessionTokenConsumed').textContent = session.tokenConsumed || 0;
            // 新增：更新退出登录按钮状态
            updateLogoutHeaderBtn();
            updateLogoutToolbarBtn();
            return fetch(`/token/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tokenCode: currentTokenCode })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.valid) {
                        document.getElementById('tokenCount').textContent = data.tokenCount;
                        document.getElementById('totalTokenConsumed').textContent = data.totalTokensConsumed || 0;
                    }
                    return session;
                });
        })
        .then(session => {
            hideGlobalLoading();

            // 显示成功提示
            showToast('会话创建成功！', 'success');

            // 只渲染，不再fetch
            refreshTableFromSession(session);

            // 显示切换按钮
            showToggleAnswerButton();

            loadMessages(session.uuid);
            loadSessions();
        })
        .catch(error => {
            hideGlobalLoading();
            console.error('上传解析失败:', error);
            showToast('创建会话失败：' + error.message, 'error');
        });
}

// 初始化Handsontable
function initHandsontable() {
    const container = document.getElementById('excelTable');
    if (!container) {
        console.error('Excel table container not found');
        return;
    }

    // 创建一个Set来存储需要高亮的单元格
    const highlightedCells = new Set();

    try {
        hot = new Handsontable(container, {
            data: [],
            rowHeaders: function (index) { return index + 1; }, // 行头123
            colHeaders: function (index) {
                let name = '';
                let idx = index;
                while (idx >= 0) {
                    name = String.fromCharCode(idx % 26 + 65) + name;
                    idx = Math.floor(idx / 26) - 1;
                }
                return name;
            }, // 列头ABC
            height: '100%',
            width: '100%',
            licenseKey: 'non-commercial-and-evaluation',
            readOnly: true,  // 只读
            contextMenu: ['copy'],  // 启用右键菜单
            manualColumnResize: true,  // 允许调整列宽
            manualRowResize: true,  // 允许调整行高
            manualColumnMove: false,  // 禁止移动列
            manualRowMove: false,  // 禁止移动行
            stretchH: 'all',  // 自动调整列宽以适应容器
            autoColumnSize: true,  // 自动调整列宽以适应内容
            wordWrap: true,  // 允许文字换行
            afterChange: function (changes, source) { },
            colWidths: 100,
            rowHeights: 23,
            cells: function (row, col) {
                const cellProperties = {};
                if (row === 0) {
                    cellProperties.className = 'table-header-row';
                }
                return cellProperties;
            },
            afterRender: function () {
                console.log('Handsontable rendered successfully');
            },
            afterError: function (error) {
                console.error('Handsontable error:', error);
            },
            afterSelectionEnd: function (row, col, row2, col2) {
                handleTableSelection(row, col, row2, col2);
            },
            afterRender: function () {
                console.log('Handsontable rendered successfully');
                // 为修改过的单元格添加悬浮提示
                setupCellTooltips();
            },
            afterOnCellMouseOver: function(event, coords, TD) {
                // 鼠标悬浮在单元格上时显示提示
                showCellTooltip(coords.row, coords.col, TD, event);
            },
            afterOnCellMouseOut: function(event, coords, TD) {
                // 鼠标离开单元格时隐藏提示
                hideCellTooltip();
            },
        });
        console.log('Handsontable initialized successfully');

        // 新增：监听表格容器尺寸变化，自动刷新表格
        const excelContainer = document.querySelector('.excel-container');
        if (window.ResizeObserver && excelContainer && hot) {
            const ro = new ResizeObserver(() => {
                if (hot) {
                    hot.refreshDimensions && hot.refreshDimensions();
                    hot.render && hot.render();
                }
            });
            ro.observe(excelContainer);
        }
    } catch (error) {
        console.error('Failed to initialize Handsontable:', error);
    }
}

// 切换会话面板
function toggleSessionPanel() {
    const panel = document.getElementById('sessionPanel');
    const icon = panel.querySelector('.bi-chevron-left, .bi-chevron-right');
    const searchButton = panel.querySelector('.search-btn');
    const searchInputArea = document.getElementById('sessionSearchInput');

    panel.classList.toggle('collapsed');

    if (panel.classList.contains('collapsed')) {
        icon.classList.remove('bi-chevron-left');
        icon.classList.add('bi-chevron-right');
        // 侧边栏收起时隐藏搜索按钮和输入框
        if (searchButton) searchButton.style.display = 'none';
        if (searchInputArea) searchInputArea.style.display = 'none';
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-left');
        // 侧边栏展开时显示搜索按钮，但不立即显示搜索框
        if (searchButton) searchButton.style.display = 'inline';
    }
}

// 切换搜索输入框的显示/隐藏
function toggleSearchInput() {
    const searchInputArea = document.getElementById('sessionSearchInput');
    searchInputArea.style.display = searchInputArea.style.display === 'none' ? 'block' : 'none';
    // 如果显示搜索框，聚焦输入框
    if (searchInputArea.style.display === 'block') {
        document.getElementById('sessionSearch').focus();
    } else {
        // 如果隐藏搜索框，清空输入并重新加载所有会话
        document.getElementById('sessionSearch').value = '';
        renderSessions(allSessions); // 显示所有会话
    }
}

// 新建对话
function createNewChat() {
    if (!currentTokenCode) {
        alert('请先输入代币码');
        return;
    }
    document.getElementById('uploadContainer').style.display = 'block';
    document.getElementById('excelTable').style.display = 'none';
    document.getElementById('toolbar').style.display = 'none';
    document.getElementById('exportBtn').style.display = 'none';
    document.getElementById('fullscreenBtn').style.display = 'none';
    document.getElementById('surveyLink').value = '';
    document.getElementById('excelFile').value = '';
    document.getElementById('surveyLinkContainer').style.display = 'none';
    document.getElementById('surveyLinkShort').textContent = '';
    document.getElementById('currentSessionTokenConsumed').textContent = '0';
    currentSessionId = null;
    document.getElementById('messageList').innerHTML = '';
    const versionControl = document.querySelector('.version-control');
    if (versionControl) {
        versionControl.style.display = 'none';
    }
    updateLogoutHeaderBtn();
    updateLogoutToolbarBtn();
    document.getElementById('sessionCostCard').style.display = 'none';
}

// 加载会话列表
function loadSessions() {
    if (!currentTokenCode) {
        console.log('No token code available');
        return;
    }

    console.log('Loading sessions for token:', currentTokenCode);

    fetch(`/ai-chat/session?tokenCode=${currentTokenCode}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                // 如果响应状态码表示错误，尝试读取并抛出错误信息
                return response.text().then(text => { throw new Error(`HTTP error! Status: ${response.status}, Message: ${text}`); });
            }
            return response.json();
        })
        .then(sessions => {
            console.log('Received sessions:', sessions);
            allSessions = sessions; // 保存所有会话数据
            renderSessions(allSessions); // 渲染所有会话
        })
        .catch(error => {
            console.error('加载会话列表失败:', error);
            const sessionList = document.getElementById('sessionList');
            sessionList.innerHTML = `<div class="text-center text-danger">加载会话列表失败: ${error.message}</div>`;
        });
}

// 渲染会话列表（可用于搜索过滤后）
function renderSessions(sessionsToRender) {
    const sessionList = document.getElementById('sessionList');
    if (!sessionsToRender || sessionsToRender.length === 0) {
        console.log('No sessions found to render');
        sessionList.innerHTML = '<div class="text-center text-muted">暂无会话记录</div>';
        return;
    }

    // 按时间分类会话
    const groupedSessions = groupSessionsByTime(sessionsToRender);
    console.log('Grouped sessions for rendering:', groupedSessions);

    // 渲染分组后的会话列表
    let htmlContent = '';
    for (const group in groupedSessions) {
        htmlContent += `<div class="session-group-header">${group}</div>`;
        groupedSessions[group].forEach(session => {
            htmlContent += `
                 <div class="session-item ${session.uuid === currentSessionId ? 'active' : ''}" data-uuid="${session.uuid}">
                     <span class="session-name">${session.sessionName}</span>
                     <div class="session-actions">
                         <button onclick="event.stopPropagation(); renameSession('${session.uuid}')">
                             <i class="bi bi-pencil"></i>
                         </button>
                         <button onclick="event.stopPropagation(); deleteSession('${session.uuid}')">
                             <i class="bi bi-trash"></i>
                         </button>
                     </div>
                 </div>
             `;
        });
    }
    sessionList.innerHTML = htmlContent;
}

// Helper function to get the time category for a session
function getSessionTimeCategory(session) {
    const now = new Date();
    // 使用lastMessageTime进行分类，如果lastMessageTime为空，则使用createTime
    const sessionTime = new Date(session.lastMessageTime || session.createTime);

    const diffTime = now.getTime() - sessionTime.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    // 设置时区偏移量，以处理跨时区问题（假设后端返回的时间是UTC或服务器本地时间）
    // 这是一个简化的处理，可能需要更精确的时区处理根据实际情况调整
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    yesterday.setDate(now.getDate() - 1);

    // 检查是否是今天
    if (sessionTime >= today) {
        return '今天';
    }
    // 检查是否是昨天
    if (sessionTime >= yesterday && sessionTime < today) {
        return '昨天';
    }
    // 检查是否是最近7天
    if (diffDays < 7) {
        return '最近7天';
    }
    // 检查是否是最近1月 (简化处理，按30天计算)
    if (diffDays < 30) {
        return '最近1月';
    }

    // 按月份或年份分类
    const sessionYear = sessionTime.getFullYear();
    const sessionMonth = sessionTime.getMonth() + 1; // getMonth() 是 0-11
    const currentYear = now.getFullYear();

    if (sessionYear === currentYear) {
        return `${sessionMonth}月`;
    } else {
        return `${sessionYear}年`;
    }
}

// Helper function to group sessions by time category
function groupSessionsByTime(sessions) {
    // 先按lastMessageTime降序排序会话，lastMessageTime为空的排在后面
    sessions.sort((a, b) => {
        const timeA = a.lastMessageTime ? new Date(a.lastMessageTime).getTime() : new Date(a.createTime).getTime();
        const timeB = b.lastMessageTime ? new Date(b.lastMessageTime).getTime() : new Date(b.createTime).getTime();
        return timeB - timeA; // 降序排列
    });

    const grouped = {};
    sessions.forEach(session => {
        const category = getSessionTimeCategory(session);
        if (!grouped[category]) {
            grouped[category] = [];
        }
        grouped[category].push(session);
    });

    // Define the desired order of categories
    const categoryOrder = ['今天', '昨天', '最近7天', '最近1月'];
    const sortedGroups = {};

    // Add predefined categories first in order
    categoryOrder.forEach(category => {
        if (grouped[category]) {
            sortedGroups[category] = grouped[category];
            delete grouped[category]; // Remove from original grouped object
        }
    });

    // Sort remaining month/year categories chronologically (most recent first)
    const remainingCategories = Object.keys(grouped).sort((a, b) => {
        // Extract year and month for comparison
        const aYearMatch = a.match(/(\d{4})年/);
        const bYearMatch = b.match(/(\d{4})年/);
        const aMonthMatch = a.match(/(\d{1,2})月/);
        const bMonthMatch = b.match(/(\d{1,2})月/);

        if (aYearMatch && bYearMatch) {
            return parseInt(bYearMatch[1]) - parseInt(aYearMatch[1]); // Sort years descending
        } else if (aMonthMatch && bMonthMatch) {
            // Sort months descending, assuming they are in the same year (handled by previous logic)
            return parseInt(bMonthMatch[1]) - parseInt(aMonthMatch[1]); // 修正：确保是按月份降序
        } else if (aYearMatch) {
            return -1; // Year comes after month
        } else if (bYearMatch) {
            return 1; // Year comes after month
        }
        return 0;
    });

    // Add sorted remaining categories to sortedGroups
    remainingCategories.forEach(category => {
        sortedGroups[category] = grouped[category];
    });

    return sortedGroups;
}

// 选择会话
function selectSession(sessionId) {
    console.log('Selecting session:', sessionId);
    currentSessionId = sessionId;
    
    // 重置状态
    surveyStructure = null;
    isShowingTextAnswers = false;
    originalData = null;
    
    // 显示切换按钮
    showToggleAnswerButton();
    
    document.querySelectorAll('.session-item').forEach(item => {
        item.classList.remove('active');
    });
    const activeItem = document.querySelector(`.session-item[data-uuid='${sessionId}']`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    loadSessionData(sessionId);
    updateLogoutHeaderBtn();
    updateLogoutToolbarBtn();
}

// 处理表格选区，支持多选
function handleTableSelection(row, col, row2, col2) {
    console.log('选区参数', row, col, row2, col2);
    const colCount = hot.countCols();
    const rowCount = hot.countRows();
    
    // 判断是否为整行选择
    const isFullRowSelection = (col === -1 || col2 === -1);
    // 判断是否为整列选择
    const isFullColSelection = (row === -1 || row2 === -1);
    // 判断当前选区是否为表头（只针对单元格选择，不包括整行整列选择）
    const isHeaderSelection = !isFullRowSelection && !isFullColSelection && (row === 0 || row2 === 0);
    // 判断是否只选中一个表头单元格
    const isSingleHeaderCell = !isFullRowSelection && !isFullColSelection && (row === 0 && row2 === 0);
    // 判断selectedRanges中是否有非表头区域
    const hasNonHeaderRange = selectedRanges.some(r => r.startRow > 0 && r.endRow > 0);
    
    // 如果只选中一个表头单元格，直接不显示
    if (isSingleHeaderCell) {
        removeAddRangeButton();
        return;
    }
    
    // 如果是表头选区且没有其它非表头区域，也不显示
    if (isHeaderSelection && !hasNonHeaderRange) {
        removeAddRangeButton();
        return;
    }
    
    // 选区起始行最小为1（即第二行数据），不允许选到表头
    let startRow = Math.max(1, Math.min(row, row2));
    let endRow = Math.max(1, Math.max(row, row2));
    let startCol = Math.min(col, col2);
    let endCol = Math.max(col, col2);
    
    // 整行选区
    if (isFullRowSelection) {
        startRow = Math.max(1, Math.min(row, row2));
        endRow = Math.max(1, Math.max(row, row2));
        startCol = 0;
        endCol = colCount - 1;
    } else if (isFullColSelection) { // 整列选区
        startRow = 1;
        endRow = rowCount - 1;
        startCol = Math.min(col, col2);
        endCol = Math.max(col, col2);
    }
    
    // 限定范围
    startRow = Math.max(1, Math.min(startRow, rowCount - 1));
    endRow = Math.max(1, Math.min(endRow, rowCount - 1));
    startCol = Math.max(0, Math.min(startCol, colCount - 1));
    endCol = Math.max(0, Math.min(endCol, colCount - 1));
    
    const key = `${startRow},${startCol},${endRow},${endCol}`;
    if (!selectedRanges.some(r => r.key === key)) {
        selectedRanges.push({ startRow, startCol, endRow, endCol, key });
    }
    
    // 悬浮按钮定位
    let cell = null;
    if (isFullRowSelection) {
        cell = hot.getCell(startRow, 0);
        console.log('整行选择，获取单元格:', startRow, 0, '结果:', cell);
    } else if (isFullColSelection) {
        cell = hot.getCell(1, startCol);
        console.log('整列选择，获取单元格:', 1, startCol, '结果:', cell);
    } else {
        cell = hot.getCell(startRow, startCol);
        console.log('普通选择，获取单元格:', startRow, startCol, '结果:', cell);
    }
    
    if (!cell) {
        console.log('单元格获取失败，尝试备用方案');
        cell = hot.getCell(1, 0);
        console.log('备用单元格:', cell);
    }
    
    if (cell) {
        const cellRect = cell.getBoundingClientRect();
        console.log('单元格位置:', cellRect);
        let absX = cellRect.left;
        let absY = cellRect.top - 36;
        const btnWidth = 160;
        const btnHeight = 36;
        // 获取表格容器
        const excelContainer = document.querySelector('.excel-container');
        const containerRect = excelContainer ? excelContainer.getBoundingClientRect() : {left:0,top:0,right:window.innerWidth,bottom:window.innerHeight,width:window.innerWidth,height:window.innerHeight};
        console.log('容器位置:', containerRect);
        // 判断超出方向
        let needScrollX = false, needScrollY = false;
        if (cellRect.left < containerRect.left || cellRect.right > containerRect.right) needScrollX = true;
        if (cellRect.top < containerRect.top || cellRect.bottom > containerRect.bottom) needScrollY = true;
        const rangeText = getExcelRange(startRow, startCol, endRow, endCol);
        const keyCopy = key;
        console.log('准备显示按钮，范围:', rangeText, '位置:', absX, absY, '需要滚动:', needScrollX, needScrollY);
        // 滚动逻辑
        if (needScrollX || needScrollY) {
            // 只横向滚动
            if (needScrollX && !needScrollY) {
                hot.scrollViewportTo(null, startCol, false, false);
            }
            // 只纵向滚动
            else if (!needScrollX && needScrollY) {
                hot.scrollViewportTo(startRow, null, false, false);
            }
            // 都需要
            else {
                hot.scrollViewportTo(startRow, startCol, false, false);
            }
            requestAnimationFrame(() => {
                const cellAfterScroll = hot.getCell(startRow, startCol) || cell;
                const cellRect2 = cellAfterScroll.getBoundingClientRect();
                let absX2 = cellRect2.left;
                let absY2 = cellRect2.top - 36;
                // 限制按钮在表格容器内
                if (absX2 < containerRect.left) absX2 = containerRect.left;
                if (absX2 + btnWidth > containerRect.right) absX2 = containerRect.right - btnWidth;
                if (absY2 < containerRect.top) absY2 = cellRect2.top + cellRect2.height + 8;
                if (absY2 + btnHeight > containerRect.bottom) absY2 = containerRect.bottom - btnHeight;
                console.log('滚动后显示按钮，位置:', absX2, absY2);
                showAddRangeButton(rangeText, keyCopy, absX2, absY2);
            });
        } else {
            // 限制按钮在表格容器内
            if (absX < containerRect.left) absX = containerRect.left;
            if (absX + btnWidth > containerRect.right) absX = containerRect.right - btnWidth;
            if (absY < containerRect.top) absY = cellRect.top + cellRect.height + 8;
            if (absY + btnHeight > containerRect.bottom) absY = containerRect.bottom - btnHeight;
            console.log('直接显示按钮，位置:', absX, absY);
            showAddRangeButton(rangeText, keyCopy, absX, absY);
        }
    } else {
        console.log('无法获取单元格，无法显示按钮');
    }

    // 判断选区是否很高，需要纵向滚动到顶部
    let needVerticalScroll = false;
    const htHolder = document.querySelector('.ht_master .wtHolder');
    if (htHolder) {
        const visibleHeight = htHolder.clientHeight;
        const rowHeight = hot.getRowHeight ? hot.getRowHeight(1) : 23; // 取一行高度
        const selectionHeight = (endRow - startRow + 1) * rowHeight;
        if (selectionHeight > visibleHeight * 0.8) {
            // 选区很高，滚动到startRow顶部
            hot.scrollViewportTo(startRow, null, true, true);
            needVerticalScroll = true;
        }
    }

    // 如果需要纵向滚动，延迟后再显示按钮，保证按钮在可视区
    if (needVerticalScroll) {
        setTimeout(() => {
            let cell = null;
            if (isFullRowSelection) {
                cell = hot.getCell(startRow, 0);
            } else if (isFullColSelection) {
                cell = hot.getCell(1, startCol);
            } else {
                cell = hot.getCell(startRow, startCol);
            }
            if (!cell) cell = hot.getCell(1, 0);
            if (cell) {
                const cellRect = cell.getBoundingClientRect();
                let absX = cellRect.left;
                let absY = cellRect.top - 36;
                const btnWidth = 160;
                const btnHeight = 36;
                const excelContainer = document.querySelector('.excel-container');
                const containerRect = excelContainer ? excelContainer.getBoundingClientRect() : {left:0,top:0,right:window.innerWidth,bottom:window.innerHeight,width:window.innerWidth,height:window.innerHeight};
                if (absX < containerRect.left) absX = containerRect.left;
                if (absX + btnWidth > containerRect.right) absX = containerRect.right - btnWidth;
                if (absY < containerRect.top) absY = cellRect.top + cellRect.height + 8;
                if (absY + btnHeight > containerRect.bottom) absY = containerRect.bottom - btnHeight;
                showAddRangeButton(getExcelRange(startRow, startCol, endRow, endCol), `${startRow},${startCol},${endRow},${endCol}`, absX, absY);
            }
        }, 120);
    }
}

// 聊天框展示所有选中区域
function showSelectedRangesInChat() {
    // 先移除所有旧的
    document.querySelectorAll('.message.selected-range').forEach(e => e.remove());
    if (!selectedRanges.length) return;
    const messageList = document.getElementById('messageList');
    selectedRanges.forEach((range, idx) => {
        const startCell = String.fromCharCode(65 + range.startCol) + (range.startRow + 1);
        const endCell = String.fromCharCode(65 + range.endCol) + (range.endRow + 1);
        const rangeText = startCell === endCell ? startCell : `${startCell}:${endCell}`;
        let tableHtml = '<table class="selected-range-table">';
        range.data.forEach(row => {
            tableHtml += '<tr>' + row.map(cell => `<td>${cell ?? ''}</td>`).join('') + '</tr>';
        });
        tableHtml += '</table>';
        const msgDiv = document.createElement('div');
        msgDiv.className = 'message selected-range';
        msgDiv.id = `selectedRangeMsg_${range.key}`;
        msgDiv.innerHTML = `
            <div>
                <b>这是我选中的表格数据${rangeText}</b>
                <button class="remove-selected-range" onclick="removeSelectedRangeMsg('${range.key}')" style="float:right;">删除</button>
            </div>
            ${tableHtml}
        `;
        messageList.appendChild(msgDiv);
    });
    messageList.scrollTop = messageList.scrollHeight;
}

// 删除某个选区
function removeSelectedRangeMsg(key) {
    selectedRanges = selectedRanges.filter(r => r.key !== key);
    showSelectedRangesInChat();
}

// 工具函数：数字转Excel列名
function colIdxToName(idx) {
    let name = '';
    idx = Number(idx);
    while (idx >= 0) {
        name = String.fromCharCode(idx % 26 + 65) + name;
        idx = Math.floor(idx / 26) - 1;
    }
    return name;
}

// 工具函数：生成Excel范围
function getExcelRange(startRow, startCol, endRow, endCol) {
    const start = colIdxToName(startCol) + (startRow + 1);
    const end = colIdxToName(endCol) + (endRow + 1);
    return start === end ? start : `${start}:${end}`;
}

// 悬浮按钮
function showAddRangeButton(rangeText, key, absX, absY) {
    console.log('showAddRangeButton', rangeText, key, absX, absY);
    removeAddRangeButton();
    addRangeBtn = document.createElement('button');
    addRangeBtn.className = 'add-range-btn';
    addRangeBtn.innerText = `添加${rangeText}到聊天`;
    addRangeBtn.style.position = 'fixed';
    addRangeBtn.style.left = absX + 'px';
    addRangeBtn.style.top = absY + 'px';
    addRangeBtn.style.zIndex = 99999; // 极高，防止被遮挡
    addRangeBtn.onclick = function () {
        insertRangeTag(rangeText, key);
        removeAddRangeButton();
    };
    document.body.appendChild(addRangeBtn);
    addRangeBtn._justCreated = true;
    setTimeout(() => {
        if (addRangeBtn) addRangeBtn._justCreated = false;
    }, 300);
    console.log('按钮已添加到DOM，位置:', absX, absY, '按钮元素:', addRangeBtn);
}
function removeAddRangeButton() {
    if (addRangeBtn) {
        addRangeBtn.remove();
        addRangeBtn = null;
    }
}
// 页面全局点击时，失焦自动移除悬浮按钮
if (!window._addRangeBtnListener) {
    document.addEventListener('mousedown', function (e) {
        if (addRangeBtn && !addRangeBtn.contains(e.target) && !addRangeBtn._justCreated) {
            removeAddRangeButton();
        }
    });
    window._addRangeBtnListener = true;
}

// 插入标签到输入框光标处
function insertRangeTag(rangeText, key) {
    const input = document.getElementById('messageInput');
    if (!input) return;

    // 如果内容为空或只包含<br>或&nbsp;，先清空
    if (
        input.innerHTML.trim() === '' ||
        input.innerHTML.trim() === '<br>' ||
        input.innerHTML.trim().replace(/&nbsp;/g, '') === ''
    ) {
        input.innerHTML = '';
    }

    // 创建span组件
    const span = document.createElement('span');
    span.className = 'range-tag';
    span.setAttribute('data-range', rangeText);
    span.setAttribute('data-key', key);
    span.setAttribute('tabindex', '0'); // 新增，确保可点击
    span.contentEditable = 'false'; // 防止被编辑
    span.innerText = rangeText;

    // 在末尾插入span
    input.appendChild(span);

    // 追加一个空格，方便继续输入
    input.appendChild(document.createTextNode(' '));

    // 聚焦并将光标移到末尾
    input.focus();
    if (window.getSelection) {
        const sel = window.getSelection();
        sel.selectAllChildren(input);
        sel.collapseToEnd();
    }
}
function removeRangeTag(btn) {
    btn.parentNode.remove();
}

// 标签点击高亮表格选区
function highlightRangeByKey(key) {
    const range = selectedRanges.find(r => r.key === key);
    if (range) {
        hot.selectCell(range.startRow, range.startCol, range.endRow, range.endCol, true);
    }
}

// 输入框退格删除标签
function setupInputDeleteTag() {
    const input = document.getElementById('messageInput');
    // 粘贴事件：支持带range-tag的HTML粘贴，禁止带入外部样式，去除StartFragment/EndFragment和所有不可见字符
    input.addEventListener('paste', function (e) {
        e.preventDefault();
        let html = (e.clipboardData || window.clipboardData).getData('text/html');
        let text = (e.clipboardData || window.clipboardData).getData('text');
        // 去除 StartFragment/EndFragment
        if (html) html = html.replace(/<!--StartFragment-->|<!--EndFragment-->/g, '');
        if (text) text = text.replace(/<!--StartFragment-->|<!--EndFragment-->/g, '');
        // 去除所有 &nbsp; 和 \u200B 和 &#8203;
        if (html) html = html.replace(/(&nbsp;|\u200B|&#8203;)/g, '');
        if (text) text = text.replace(/(&nbsp;|\u200B|&#8203;)/g, '');

        // 新增：如果是@@这是选中的数据范围A2:A13@@这种文本，自动转为span组件
        const rangeTagRegex = /@@这是选中的数据范围([A-Z]+\d+(?::[A-Z]+\d+)?)@@/g;
        if (text && rangeTagRegex.test(text)) {
            // 替换所有匹配为span组件
            const replaced = text.replace(rangeTagRegex, function(match, range) {
                return `<span class=\"range-tag\" data-range=\"${range}\" tabindex=\"0\" contenteditable=\"false\">${range}</span>`;
            });
            document.execCommand('insertHTML', false, replaced);
        } else if (html && html.includes('range-tag')) {
            // 只保留 .range-tag，去除其它样式
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            let result = '';
            doc.body.childNodes.forEach(node => {
                if (node.nodeType === 1 && node.classList.contains('range-tag')) {
                    // 重新创建干净的 span
                    const span = document.createElement('span');
                    span.className = 'range-tag';
                    span.setAttribute('data-range', node.getAttribute('data-range'));
                    span.setAttribute('data-key', node.getAttribute('data-key') || '');
                    span.contentEditable = 'false';
                    span.innerText = node.innerText;
                    result += span.outerHTML;
                } else if (node.nodeType === 1 && node.classList.contains('select-anchor')) {
                    // 只保留一个 anchor，不插入内容
                    result += '<span class="select-anchor" contenteditable="false"></span>';
                } else {
                    // 普通文本，去除不可见字符
                    result += (node.textContent || '').replace(/[\u200B\u00A0]/g, '');
                }
            });
            document.execCommand('insertHTML', false, result);
        } else {
            document.execCommand('insertText', false, text);
        }
        // 粘贴后清理多余 anchor 和不可见字符
        setTimeout(() => {
            cleanInputAnchors(input);
            cleanInvisibleChars(input);
        }, 0);
    });
    // 退格整块删除和标签点击高亮（优化：优先删文字，只有紧贴组件时才整组删除，兼容聊天区复制结构）
    input.addEventListener('keydown', function (e) {
        if (e.key === 'Backspace') {
            const sel = window.getSelection();
            if (sel.rangeCount) {
                const range = sel.getRangeAt(0);
                let node = sel.anchorNode;
                let offset = sel.anchorOffset;

                // 1. 光标在 range-tag 内部
                if (node.nodeType === 1 && node.classList.contains('range-tag')) {
                    removeRangeComponent(node);
                    e.preventDefault();
                    return;
                }
                // 2. 光标在 select-anchor 内部
                if (node.nodeType === 1 && node.classList.contains('select-anchor')) {
                    let tag = node.nextSibling && node.nextSibling.classList?.contains('range-tag') ? node.nextSibling : node.previousSibling;
                    if (tag && tag.classList?.contains('range-tag')) {
                        removeRangeComponent(tag);
                        e.preventDefault();
                        return;
                    }
                }
                // 3. 光标在文本节点
                if (node.nodeType === 3) {
                    // offset > 0，优先删文字内容
                    if (offset > 0) {
                        // 如果前面是不可见字符，自动删掉
                        let prevChar = node.textContent[offset - 1];
                        if (/[ B A0\s]/.test(prevChar)) {
                            // 删除不可见字符
                            node.textContent = node.textContent.slice(0, offset - 1) + node.textContent.slice(offset);
                            sel.collapse(node, offset - 1);
                            e.preventDefault();
                            return;
                        }
                        // 否则让浏览器默认行为（删文字）
                        return;
                    }
                    // offset == 0，前面是 anchor 或组件时才整组删除
                    let prev = node.previousSibling;
                    // 连续删除不可见字符
                    while (prev && prev.nodeType === 3 && /^[\u200B\u00A0\s]*$/.test(prev.textContent)) {
                        let toRemove = prev;
                        prev = prev.previousSibling;
                        toRemove.remove();
                    }
                    if (prev && prev.classList?.contains('select-anchor')) {
                        let prev2 = prev.previousSibling;
                        if (prev2 && prev2.classList?.contains('range-tag')) {
                            removeRangeComponent(prev2);
                            e.preventDefault();
                            return;
                        }
                    }
                }
            }
        }
    });
    // 删除 range-tag 及其前后 anchor 的函数
    function removeRangeComponent(tag) {
        if (!tag || !tag.classList?.contains('range-tag')) return;
        let prev = tag.previousSibling;
        let next = tag.nextSibling;
        if (prev && prev.classList?.contains('select-anchor')) prev.remove();
        if (next && next.classList?.contains('select-anchor')) next.remove();
        tag.remove();
    }
    // 粘贴后清理所有不可见字符
    function cleanInvisibleChars(input) {
        Array.from(input.childNodes).forEach(node => {
            if (node.nodeType === 3 && /[\u200B\u00A0]/.test(node.textContent)) {
                node.textContent = node.textContent.replace(/[\u200B\u00A0]/g, '');
            }
        });
    }

    // 新增：每次内容变化后，给所有.range-tag绑定点击事件
    function bindRangeTagClick() {
        input.querySelectorAll('.range-tag').forEach(tag => {
            tag.onclick = function (e) {
                e.stopPropagation(); // 防止冒泡
                highlightRangeByText(tag.getAttribute('data-range'));
            };
        });
    }
    input.addEventListener('input', bindRangeTagClick);
    // 初始化时也绑定一次
    bindRangeTagClick();

    // 事件委托，保证所有.range-tag都能响应点击
    input.addEventListener('click', function(e) {
        const tag = e.target.closest('.range-tag');
        if (tag) {
            e.stopPropagation();
            highlightRangeByText(tag.getAttribute('data-range'));
        }
    });
}

// 支持点击标签高亮表格区域
function highlightRangeByText(rangeText) {
    // 解析如B2:C3
    const parseCell = cell => {
        const match = cell.match(/^([A-Z]+)(\d+)$/);
        if (!match) return null;
        let col = 0;
        for (let i = 0; i < match[1].length; i++) {
            col = col * 26 + (match[1].charCodeAt(i) - 65 + 1);
        }
        col -= 1;
        let row = parseInt(match[2], 10) - 1;
        row = Math.max(1, row); // 不允许选到第0行
        return { row, col };
    };
    let start = rangeText, end = rangeText;
    if (rangeText.includes(':')) {
        [start, end] = rangeText.split(':');
    }
    const s = parseCell(start);
    const e = parseCell(end);
    if (s && e) {
        hot.selectCell(s.row, s.col, e.row, e.col, true);
        // 只滚动到可见，不做居中
        hot.scrollViewportTo(s.row, s.col, true, true);
        setTimeout(() => {
            const cell = hot.getCell(s.row, s.col);
            if (cell) {
                const cellRect = cell.getBoundingClientRect();
                const excelContainer = document.querySelector('.excel-container');
                const containerRect = excelContainer ? excelContainer.getBoundingClientRect() : {left:0,top:0,right:window.innerWidth,bottom:window.innerHeight,width:window.innerWidth,height:window.innerHeight};
                let absX = cellRect.left;
                let absY = cellRect.top - 36;
                const btnWidth = 160;
                const btnHeight = 36;
                if (absX < containerRect.left) absX = containerRect.left;
                if (absX + btnWidth > containerRect.right) absX = containerRect.right - btnWidth;
                if (absY < containerRect.top) absY = cellRect.top + cellRect.height + 8;
                if (absY + btnHeight > containerRect.bottom) absY = containerRect.bottom - btnHeight;
                showAddRangeButton(getExcelRange(s.row, s.col, e.row, e.col), `${s.row},${s.col},${e.row},${e.col}`, absX, absY);
            }
        }, 80);
    }
}

// 聊天记录区标签点击高亮（事件委托，防止事件丢失）
window.addEventListener('DOMContentLoaded', function () {
    document.getElementById('messageList').addEventListener('click', function (e) {
        // 只保留高亮，不自动全选标签
        // if (e.target.classList.contains('range-tag')) {
        //     // 自动选中整个标签，便于复制
        //     const range = document.createRange();
        //     range.selectNodeContents(e.target);
        //     const sel = window.getSelection();
        //     sel.removeAllRanges();
        //     sel.addRange(range);
        // }
    });
});

// 加载版本历史
function loadVersionHistory(forceSelectCurrent = false) {
    console.log('loadVersionHistory called, currentSessionId:', currentSessionId, 'forceSelectCurrent:', forceSelectCurrent);
    if (!currentSessionId) {
        console.log('No session ID available, skipping version history load');
        return;
    }
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect) {
        console.error('Version select element not found');
        return;
    }

    // 保存当前选中的版本ID（如果有的话），但如果强制选择当前版本则忽略
    const currentSelectedId = forceSelectCurrent ? null : versionSelect.value;
    
    versionSelect.innerHTML = '<option value="">加载版本历史...</option>';
    versionSelect.disabled = true;
    fetch(`/ai-chat/excel/versions?sessionId=${currentSessionId}`)
        .then(response => {
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return response.json();
        })
        .then(data => {
            const versions = Array.isArray(data.data) ? data.data : [];
            if (versions.length === 0) {
                versionSelect.innerHTML = '<option value="">暂无调整数据历史</option>';
                return;
            }
            versionSelect.innerHTML = versions.map((version, idx) => {
                // 时间格式化
                let timeStr = '无时间';
                if (version.createTime) {
                    const date = new Date(version.createTime);
                    if (!isNaN(date.getTime())) {
                        timeStr = date.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' });
                    } else {
                        timeStr = version.createTime;
                    }
                }
                // 修改摘要
                let summary = '';
                let hasModifications = false;
                try {
                    const mods = JSON.parse(version.dataModifications || '[]');
                    if (mods.length > 0) {
                        hasModifications = true;
                        summary += ` ${mods.length}处修改 - ${mods[0].content?.slice(0, 20) || ''}`;
                    }
                } catch { }

                // 聊天内容摘要
                let chatSummary = version.content ? ` - "${version.content.slice(0, 40)}"` : '';

                // 第一个版本（idx === 0）是当前版本
                const isCurrentVersion = idx === 0;

                // 判断是否是初始上传版本（通过后端标识或ID判断）
                const isInitialVersion = version.isInitialVersion === true || String(version.id).startsWith('initial_');

                let versionName;
                if (isCurrentVersion) {
                    versionName = `当前版本V${versions.length - idx}`;
                } else if (isInitialVersion) {
                    versionName = `初始版本V${versions.length - idx}`;
                } else {
                    versionName = `历史版本V${versions.length - idx}`;
                }
                
                // 如果是重新加载且之前选中的版本仍然存在，则保持选中状态
                const isSelected = currentSelectedId && version.id === currentSelectedId ? 'selected' : '';
                
                return `<option value="${version.id}" data-message-id="${version.id}" ${isCurrentVersion ? 'data-current="true"' : ''} ${isSelected}>
                    ${versionName}  ${timeStr}${summary}${chatSummary}
                </option>`;
            }).join('');
        })
        .catch(error => {
            versionSelect.innerHTML = '<option value="">加载失败</option>';
            console.error('加载版本历史失败:', error);
        })
        .finally(() => {
            versionSelect.disabled = false;
            updateVersionButtons();

            // 如果之前有选中的版本且不是强制选择当前版本，设置程序化变更标志
            if (currentSelectedId) {
                versionSelect.dataset.programmaticChange = 'true';
            } else if (forceSelectCurrent && versionSelect.options.length > 0) {
                // 强制选择当前版本（第一个选项）
                versionSelect.selectedIndex = 0;
                versionSelect.dataset.programmaticChange = 'true';
                // 触发change事件以确保UI状态正确
                versionSelect.dispatchEvent(new Event('change'));
            }
        });
}

// 更新版本按钮状态
function updateVersionButtons() {
    const versionSelect = document.getElementById('versionSelect');
    const selectedOption = versionSelect.options[versionSelect.selectedIndex];
    const restoreBtn = document.getElementById('restoreVersionBtn');

    if (restoreBtn) {
        // 如果没有选择版本，禁用恢复按钮
        if (!selectedOption) {
            restoreBtn.disabled = true;
            return;
        }

        // 获取当前选中的版本索引
        const selectedIndex = versionSelect.selectedIndex;
        // 如果是第一个版本（当前版本），禁用恢复按钮
        restoreBtn.disabled = selectedIndex === 0;
    }
}

function showPreviewTip() {
    // 检查今天是否已经显示过提示
    const today = new Date().toDateString();
    const lastShown = localStorage.getItem('previewTipLastShown');

    if (lastShown === today) {
        return; // 今天已经显示过，不再显示
    }

    // 移除旧的提示框（如果存在）
    const oldTip = document.getElementById('previewTipModal');
    if (oldTip) {
        oldTip.remove();
    }

    // 创建新的模态框
    const modalHtml = `
        <div class="modal fade" id="previewTipModal" tabindex="-1" aria-labelledby="previewTipModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="previewTipModalLabel">历史版本预览</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info mb-0">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            当前浏览的是历史版本。<br>
                            <b>⚠️ AI助手对数据的所有调整都只会基于最新版本的Excel数据进行，可以点击【恢复】按钮将历史版本恢复为最新版本</b>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dontShowAgainToday">
                            <label class="form-check-label" for="dontShowAgainToday">
                                今天不再提示
                            </label>
                        </div>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我知道了</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHtml;
    document.body.appendChild(modalContainer);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('previewTipModal'));
    modal.show();

    // 监听模态框关闭事件
    document.getElementById('previewTipModal').addEventListener('hidden.bs.modal', function () {
        // 如果用户勾选了"今天不再提示"，记录到 localStorage
        if (document.getElementById('dontShowAgainToday').checked) {
            localStorage.setItem('previewTipLastShown', today);
        }
        this.remove();
    });
}

// 恢复版本
function restoreVersion() {
    const versionSelect = document.getElementById('versionSelect');
    const selectedId = versionSelect.value;
    const selectedOption = versionSelect.options[versionSelect.selectedIndex];

    if (!selectedId || !selectedOption) {
        alert('请先选择一个版本');
        return;
    }

    // 检查是否是当前版本（第一个版本）
    if (versionSelect.selectedIndex === 0) {
        alert('当前版本无需恢复');
        return;
    }

    if (!currentSessionId) {
        alert('未找到当前会话');
        return;
    }

    if (!confirm('确定要恢复到此版本吗？这将创建一个新的版本记录。')) return;

    fetch(`/ai-chat/excel/version/${selectedId}/restore?sessionId=${currentSessionId}`, {
        method: 'POST'
    })
        .then(response => {
            // 克隆响应以便可以读取两次 (一次text, 一次json)
            const clonedResponse = response.clone();

            // 先尝试读取文本，用于调试
            clonedResponse.text().then(text => {
                console.log('恢复版本原始响应文本:', text);
            }).catch(e => {
                console.error('读取恢复版本响应文本失败:', e);
            });

            if (!response.ok) {
                // 如果响应状态码不是2xx，尝试读取错误信息
                return response.text().then(text => {
                    throw new Error(`恢复版本失败: ${response.status} ${response.statusText} - ${text}`);
                });
            }
            // 成功时，尝试读取JSON
            return response.json();
        })
        .then(data => {
            console.log('恢复版本成功响应数据:', data);
            // 在聊天区插入系统消息
            let timeStr = selectedOption.textContent.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/);
            timeStr = timeStr ? timeStr[0] : '';
            addMessage('system', `已将${timeStr}的版本恢复为最新版本`);
            alert('版本恢复成功');
            // 重新加载会话数据
            loadSessionData(currentSessionId);
            
            // 确保版本选择框回到当前版本状态
            setTimeout(() => {
                const versionSelect = document.getElementById('versionSelect');
                if (versionSelect && versionSelect.options.length > 0) {
                    versionSelect.selectedIndex = 0; // 选择第一个选项（当前版本）
                    // 触发change事件以更新UI状态
                    versionSelect.dispatchEvent(new Event('change'));
                }
            }, 100);
        })
        .catch(error => {
            console.error('恢复版本请求失败:', error);
            alert('恢复版本失败: ' + error.message);
        });
}

// 版本选择高亮聊天记录并预览表格
const versionSelect = document.getElementById('versionSelect');
if (versionSelect) {
    versionSelect.addEventListener('change', function () {
        const selectedId = this.value;
        const selectedIndex = this.selectedIndex; // 获取选中的索引
        const isProgrammaticChange = this.dataset.programmaticChange === 'true';
        
        // 清除程序化变更标志
        delete this.dataset.programmaticChange;

        // 聊天高亮
        document.querySelectorAll('.chat-message').forEach(msg => {
            msg.classList.remove('highlight');
            if (msg.dataset.messageId === selectedId) {
                msg.classList.add('highlight');
                msg.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });

        // 预览表格：如果是第一个选项（当前版本），则退出预览；否则进入预览
        // 只有在非程序化变更时才执行预览逻辑
        if (!isProgrammaticChange) {
            if (selectedIndex === 0) {
                exitPreviewVersion();
            } else if (selectedId) {
                previewVersion(selectedId, true);
            }
        }

        // 更新按钮状态
        updateVersionButtons();
    });
}

function previewVersion(messageId, isSelect) {
    if (!currentSessionId || !messageId) return;
    fetch(`/ai-chat/excel/version/${messageId}?sessionId=${currentSessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.excelData) {
                try {
                    // 解析 JSON 字符串为数组
                    const parsedData = JSON.parse(data.excelData);
                    refreshTable(parsedData);

                    // 高亮修改的单元格并绑定悬浮事件
                    if (data.dataModifications) {
                        try {
                            const modifications = JSON.parse(data.dataModifications);
                            modifications.forEach(mod => {
                                if (mod.length >= 3) {
                                    const [row, col, value] = mod;
                                    hot.setCellMeta(row, col, 'className', 'cell-modified');
                                }
                            });
                            hot.render();
                        } catch (e) {
                            console.error('解析修改数据失败:', e);
                        }
                    }

                    // 为当前预览版本绑定悬浮事件（显示与上一版本的差异）
                    setTimeout(() => {
                        bindTooltipEventsForVersion(messageId);
                    }, 100); // 延迟确保表格渲染完成

                    document.querySelector('.excel-container').classList.add('preview-version');

                    // 显示"切换为目前版本"按钮
                    const switchToCurrentBtn = document.getElementById('switchToCurrentBtn');
                    if (switchToCurrentBtn) {
                        switchToCurrentBtn.style.display = 'inline-block';
                    }

                    // 只在从最新版本切换到历史版本时显示提示
                    const versionSelect = document.getElementById('versionSelect');
                    // 使用selectedIndex来判断是否是当前版本（第一个选项）
                    if (versionSelect && versionSelect.selectedIndex !== 0) {
                        showPreviewTip();
                    }

                } catch (e) {
                    console.error('解析版本数据失败:', e);
                    alert('解析版本数据失败');
                }
            } else {
                console.error('版本数据格式错误:', data);
                alert('版本数据格式错误');
            }
        })
        .catch(error => {
            console.error('加载版本数据失败:', error);
            alert('加载版本数据失败: ' + error.message);
        });
}

function exitPreviewVersion() {
    document.querySelector('.excel-container').classList.remove('preview-version');
    hidePreviewTip();

    // 隐藏"切换为目前版本"按钮
    const switchToCurrentBtn = document.getElementById('switchToCurrentBtn');
    if (switchToCurrentBtn) {
        switchToCurrentBtn.style.display = 'none';
    }

    // 重新加载会话数据，回到最新版本的数据状态
    loadSessionData(currentSessionId);
}

function hidePreviewTip() {
    const modal = document.getElementById('previewTipModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
        modal.remove();
    }
}

// 刷新表格数据
function refreshTable(data) {
    if (!hot) {
        console.error('Handsontable instance not found');
        return;
    }
    if (!data || !Array.isArray(data)) {
        console.error('Invalid data format for table refresh');
        return;
    }
    hot.loadData(data);
    hot.render();
}

// 修改表格渲染，支持cellStyles
function renderAnalysisTables(tables) {
    if (!tables || tables.length === 0) return '';
    if (!window.analysisTablesMap) window.analysisTablesMap = {};
    let html = '';
    const uniquePrefix = 'analysisTable_' + Date.now() + '_' + Math.floor(Math.random() * 100000);
    tables.forEach((table, index) => {
        const tableId = uniquePrefix + '_' + index;
        window.analysisTablesMap[tableId] = table;
        html += `<div class="analysis-table-container">
            <div class="analysis-table-container-header">
                <h4>${table.title}</h4>
                <div>
                    <button class="table-fullscreen-btn icon-only" title="全屏" onclick="showTableFullscreenById('${tableId}')">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                    <button class="table-download-btn icon-only" title="下载" onclick="downloadTableAsExcelById('${tableId}')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="${tableId}">
                    <thead>
                        ${table.headers && table.headers.length > 0 ? renderTableHeaderWithStyles(table) : ''}
                    </thead>
                    <tbody>
                        ${renderTableRowsWithStyles(table)}
                    </tbody>
                </table>
            </div>
        </div>`;
    });

    // 添加全屏预览模态框
    if (!document.getElementById('tableFullscreenModal')) {
        const modalHtml = `
            <div class="table-fullscreen-modal" id="tableFullscreenModal">
                <div class="table-fullscreen-header">
                    <div class="table-fullscreen-title"></div>
                    <div class="table-fullscreen-actions">
                        <button class="table-download-btn icon-only" title="下载" onclick="downloadCurrentFullscreenTable()">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="table-fullscreen-btn icon-only" title="关闭" onclick="closeTableFullscreen()">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div class="table-fullscreen-content"></div>
            </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    return html;
}

// 渲染表头，自动加粗
function renderTableHeaderWithStyles(table) {
    if (!table.headers || table.headers.length === 0) return '';
    let html = '<tr>';
    for (let j = 0; j < table.headers.length; j++) {
        html += `<th style="font-weight:bold;">${table.headers[j]}</th>`;
    }
    html += '</tr>';
    return html;
}

// 渲染数据区，支持cellStyles
function renderTableRowsWithStyles(table) {
    const merges = Array.isArray(table.cellMerges) ? table.cellMerges : [];
    const mergeMap = {};
    merges.forEach(m => {
        mergeMap[`${m.row},${m.col}`] = m;
    });
    const skip = {};
    let html = '';
    for (let i = 0; i < table.rows.length; i++) {
        html += '<tr>';
        for (let j = 0; j < table.rows[i].length; j++) {
            if (skip[`${i},${j}`]) continue;
            const merge = mergeMap[`${i},${j}`];
            let attrs = '';
            if (merge) {
                if (merge.rowspan > 1) attrs += ` rowspan="${merge.rowspan}"`;
                if (merge.colspan > 1) attrs += ` colspan="${merge.colspan}"`;
                if (merge.rowspan > 1 || merge.colspan > 1) {
                    for (let r = 0; r < merge.rowspan; r++) {
                        for (let c = 0; c < merge.colspan; c++) {
                            if (r !== 0 || c !== 0) skip[`${i + r},${j + c}`] = true;
                        }
                    }
                }
            }
            // 处理样式
            let style = '';
            if (table.cellStyles && table.cellStyles[i] && table.cellStyles[i][j]) {
                const cellStyle = table.cellStyles[i][j];
                if (cellStyle.fontWeight) style += `font-weight:${cellStyle.fontWeight};`;
                if (cellStyle.color) style += `color:${cellStyle.color};`;
            }
            html += `<td${attrs}${style ? ` style=\"${style}\"` : ''}>${table.rows[i][j]}</td>`;
        }
        html += '</tr>';
    }
    return html;
}

// 修改showTableFullscreenById，支持双层表头
function showTableFullscreenById(tableId) {
    const modal = document.getElementById('tableFullscreenModal');
    const tableData = window.analysisTablesMap ? window.analysisTablesMap[tableId] : null;
    if (!tableData) return;
    let html = `
        <div class="analysis-table-container" style="width:100%;margin:0;max-width:100%;">
            <div class="table-responsive" style="width:100%;margin:0;max-width:100%;">
                <table class="table table-bordered table-striped" style="width:100%;margin:0;max-width:100%;">
                    <thead>
                        ${renderTableHeaderWithStyles(tableData)}
                    </thead>
                    <tbody>
                        ${renderTableRowsWithStyles(tableData)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    modal.querySelector('.table-fullscreen-title').textContent = tableData.title;
    modal.querySelector('.table-fullscreen-content').innerHTML = html;
    modal.setAttribute('data-table-id', tableId);
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// 新的下载函数，按tableId查找
function downloadTableAsExcelById(tableId) {
    const tableData = window.analysisTablesMap ? window.analysisTablesMap[tableId] : null;
    if (!tableData) return;
    // 构造临时table元素
    let table = document.createElement('table');
    table.setAttribute('id', tableId); // 设置id，用于获取样式信息
    
    // 只有当headers存在且不为空时才添加表头
    if (tableData.headers && tableData.headers.length > 0) {
        let thead = document.createElement('thead');
        let trHead = document.createElement('tr');
        tableData.headers.forEach(h => {
            let th = document.createElement('th');
            th.textContent = h;
            trHead.appendChild(th);
        });
        thead.appendChild(trHead);
        table.appendChild(thead);
    }
    
    let tbody = document.createElement('tbody');
    tableData.rows.forEach(row => {
        let tr = document.createElement('tr');
        row.forEach(cell => {
            let td = document.createElement('td');
            // 关键修改：强制以文本格式保存，避免Excel解析为数字
            td.textContent = String(cell);
            // 设置单元格格式为文本
            td.setAttribute('data-type', 'text');
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
    table.appendChild(tbody);
    exportTableToExcel(table, tableData.title || '分析表格');
}


// 关闭全屏预览
function closeTableFullscreen() {
    const modal = document.getElementById('tableFullscreenModal');
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

// 下载当前全屏预览的表格
function downloadCurrentFullscreenTable() {
    const modal = document.getElementById('tableFullscreenModal');
    const tableIndex = modal.getAttribute('data-table-id');
    downloadTableAsExcelById(tableIndex);
}

// 导出表格为Excel文件
function exportTableToExcel(table, title) {
    // 从table元素提取数据
    const rows = [];
    const thead = table.querySelector('thead');
    if (thead) {
        const headerRow = thead.querySelector('tr');
        if (headerRow) {
            const headerCells = headerRow.querySelectorAll('th');
            const headerData = Array.from(headerCells).map(cell => cell.textContent);
            rows.push(headerData);
        }
    }
    
    const tbody = table.querySelector('tbody');
    if (tbody) {
        const dataRows = tbody.querySelectorAll('tr');
        dataRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const rowData = Array.from(cells).map(cell => cell.textContent);
            rows.push(rowData);
        });
    }
    
    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(rows);
    
    // 获取表格数据（用于样式和合并信息）
    const tableId = table.getAttribute('id');
    const tableData = window.analysisTablesMap ? window.analysisTablesMap[tableId] : null;
    
    // 设置单元格样式和合并
    if (tableData) {
        // 处理合并单元格
        if (tableData.cellMerges && tableData.cellMerges.length > 0) {
            // 动态判断表头行数（目前只支持单行表头或无表头）
            const headerRows = (tableData.headers && tableData.headers.length > 0) ? 1 : 0;
            ws['!merges'] = tableData.cellMerges.map(merge => ({
                s: { r: merge.row + headerRows, c: merge.col },
                e: { r: merge.row + merge.rowspan - 1 + headerRows, c: merge.col + merge.colspan - 1 }
            }));
        }
        
        // 处理单元格样式
        if (tableData.cellStyles && tableData.cellStyles.length > 0) {
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cellAddress = XLSX.utils.encode_cell({r: R, c: C});
                    if (!ws[cellAddress]) continue;
                    
                    // 设置单元格格式为文本
                    ws[cellAddress].t = 's';
                    
                    // 应用样式
                    if (tableData.cellStyles[R] && tableData.cellStyles[R][C]) {
                        const cellStyle = tableData.cellStyles[R][C];
                        ws[cellAddress].s = {};
                        
                        if (cellStyle.fontWeight === 'bold') {
                            ws[cellAddress].s.font = { bold: true };
                        }
                        
                        if (cellStyle.color) {
                            if (!ws[cellAddress].s.font) ws[cellAddress].s.font = {};
                            ws[cellAddress].s.font.color = { rgb: cellStyle.color.replace('#', '') };
                        }
                    }
                    
                    // 为所有单元格设置居中对齐
                    if (!ws[cellAddress].s) ws[cellAddress].s = {};
                    ws[cellAddress].s.alignment = {
                        horizontal: 'center',
                        vertical: 'center'
                    };
                }
            }
        }
    }
    
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    const fileName = `${title}_${formatDate(new Date())}.xlsx`;
    XLSX.writeFile(wb, fileName);
}

// 格式化日期
function formatDate(date) {
    return `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}${String(date.getSeconds()).padStart(2, '0')}`;
}


// 添加表格渲染函数
function renderTable(table) {
    const container = document.createElement('div');
    container.className = 'table-container';

    if (table.title) {
        const title = document.createElement('div');
        title.className = 'table-title';
        title.textContent = table.title;
        container.appendChild(title);
    }

    const tableEl = document.createElement('table');
    tableEl.className = 'analysis-table';
    tableEl.style.width = 'auto';

    // 添加表头
    if (table.headers && table.headers.length > 0) {
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        table.headers.forEach(header => {
            const th = document.createElement('th');
            th.style.whiteSpace = 'nowrap';
            // 创建一个内部div来包裹内容
            const contentDiv = document.createElement('div');
            contentDiv.style.whiteSpace = 'nowrap';
            contentDiv.textContent = header;
            th.appendChild(contentDiv);
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        tableEl.appendChild(thead);
    }

    // 添加数据行
    if (table.rows && table.rows.length > 0) {
        const tbody = document.createElement('tbody');
        table.rows.forEach(row => {
            const tr = document.createElement('tr');
            row.forEach((cell, index) => {
                const td = document.createElement('td');
                td.style.whiteSpace = 'nowrap';
                // 创建一个内部div来包裹内容
                const contentDiv = document.createElement('div');
                contentDiv.style.whiteSpace = 'nowrap';

                // 检查是否为数字
                if (!isNaN(cell) && cell !== '') {
                    td.className = 'number';
                    contentDiv.textContent = cell;
                } else if (typeof cell === 'string' && cell.startsWith('data:image')) {
                    // 处理图片
                    const img = document.createElement('img');
                    img.src = cell;
                    img.onclick = () => showImageModal(cell);
                    contentDiv.appendChild(img);
                } else {
                    contentDiv.textContent = cell;
                }
                td.appendChild(contentDiv);
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
        tableEl.appendChild(tbody);
    }

    container.appendChild(tableEl);
    return container;
}

// 图片放大显示函数
function showImageModal(imgSrc) {
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'image-modal';
        modal.innerHTML = `
            <span class="close">&times;</span>
            <img id="modalImage">
        `;
        document.body.appendChild(modal);

        modal.querySelector('.close').onclick = () => {
            modal.style.display = 'none';
        };

        window.onclick = (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };
    }

    const modalImg = modal.querySelector('#modalImage');
    modalImg.src = imgSrc;
    modal.style.display = 'block';
}

function scrollToBottom() {
    const messageList = document.getElementById('messageList');
    if (messageList) {
        messageList.scrollTop = messageList.scrollHeight;
    }
}

// 切换答案显示功能
async function toggleAnswerDisplay() {
    if (!currentSessionId) {
        alert('请先选择一个会话');
        return;
    }

    try {
        // 如果没有问卷结构，先获取
        if (!surveyStructure) {
            await loadSurveyStructure();
        }

        if (!surveyStructure) {
            alert('无法获取问卷结构信息');
            return;
        }

        // 切换显示模式
        isShowingTextAnswers = !isShowingTextAnswers;
        
        // 更新按钮文本
        updateToggleButtonText();
        
        // 转换数据并刷新表格
        await convertAndRefreshData();
        
    } catch (error) {
        console.error('切换答案显示失败:', error);
        alert('切换答案显示失败: ' + error.message);
    }
}

// 加载问卷结构
async function loadSurveyStructure() {
    try {
        const response = await fetch(`/ai-chat/session/uuid/${currentSessionId}/survey-structure`);
        if (!response.ok) {
            throw new Error('获取问卷结构失败');
        }
        const data = await response.json();
        surveyStructure = data.surveyData;
        console.log('问卷结构加载成功:', surveyStructure);
    } catch (error) {
        console.error('加载问卷结构失败:', error);
        throw error;
    }
}

// 更新切换按钮文本
function updateToggleButtonText() {
    const button = document.getElementById('toggleAnswerBtn');
    const fullscreenButton = document.getElementById('fullscreenToggleAnswerBtn');
    
    const buttonText = isShowingTextAnswers ? '显示序号答案' : '显示文本答案';
    const icon = isShowingTextAnswers ? 'bi-hash' : 'bi-text-paragraph';
    
    if (button) {
        button.innerHTML = `<i class="bi ${icon}"></i> ${buttonText}`;
    }
    if (fullscreenButton) {
        fullscreenButton.innerHTML = `<i class="bi ${icon}"></i> ${buttonText}`;
    }
}

// 转换数据并刷新表格
async function convertAndRefreshData() {
    try {
        // 获取当前表格数据
        const currentData = hot.getData();
        
        if (!originalData) {
            // 第一次切换时保存原始数据
            originalData = JSON.parse(JSON.stringify(currentData));
        }
        
        // 转换数据
        const convertedData = convertData(currentData, isShowingTextAnswers);
        
        // 刷新表格
        hot.loadData(convertedData);
        
        console.log(`已切换到${isShowingTextAnswers ? '文本' : '序号'}答案显示模式`);
        
    } catch (error) {
        console.error('转换数据失败:', error);
        throw error;
    }
}

// 数据转换函数
function convertData(data, toText) {
    if (!surveyStructure || !data) {
        return data;
    }
    
    const convertedData = JSON.parse(JSON.stringify(data));
    
    // 遍历每个题目结构
    surveyStructure.forEach(question => {
        if (!question.colIndices || !question.options) {
            return;
        }
        
        // 创建选项映射
        const optionMap = {};
        question.options.forEach((option, index) => {
            optionMap[String(index + 1)] = option;
        });
        
        // 转换对应列的数据
        question.colIndices.forEach(colIndex => {
            const col = colIndex - 1; // 转换为0基索引
            
            // 遍历所有行（从第2行开始，跳过表头）
            for (let row = 1; row < convertedData.length; row++) {
                if (convertedData[row] && convertedData[row][col] !== undefined) {
                    const cellValue = convertedData[row][col];
                    
                    if (toText) {
                        // 序号转文本
                        if (question.type === "4" || question.type === "6multiple") {
                            // 多选题：1表示选中，0或其他值表示未选中
                            if (cellValue === "1") {
                                convertedData[row][col] = "选中";
                            } else {
                                convertedData[row][col] = "未选中";
                            }
                        } else {
                            // 单选题：使用选项文本
                            if (optionMap[cellValue]) {
                                convertedData[row][col] = optionMap[cellValue];
                            }
                        }
                    } else {
                        // 文本转序号
                        if (question.type === "4" || question.type === "6multiple") {
                            // 多选题：选中转1，未选中转0
                            if (cellValue === "选中") {
                                convertedData[row][col] = "1";
                            } else {
                                convertedData[row][col] = "0";
                            }
                        } else {
                            // 单选题：文本转序号
                            const textToNumber = Object.entries(optionMap).find(([num, text]) => text === cellValue);
                            if (textToNumber) {
                                convertedData[row][col] = textToNumber[0];
                            }
                        }
                    }
                }
            }
        });
    });
    
    return convertedData;
}

// 显示切换按钮
function showToggleAnswerButton() {
    const button = document.getElementById('toggleAnswerBtn');
    const fullscreenButton = document.getElementById('fullscreenToggleAnswerBtn');
    
    if (button) {
        button.style.display = 'inline-block';
    }
    if (fullscreenButton) {
        fullscreenButton.style.display = 'inline-block';
    }
    
    // 初始化按钮文本
    updateToggleButtonText();
}

// 隐藏切换按钮
function hideToggleAnswerButton() {
    const button = document.getElementById('toggleAnswerBtn');
    const fullscreenButton = document.getElementById('fullscreenToggleAnswerBtn');
    
    if (button) {
        button.style.display = 'none';
    }
    if (fullscreenButton) {
        fullscreenButton.style.display = 'none';
    }
}

// 选区高亮.range-tag
(function(){
    // 插入高亮样式
    const style = document.createElement('style');
    style.innerHTML = `
    .range-tag.selected {
        background: #b2f0c0 !important;
        border-color: #1abc9c !important;
        color: #1abc9c !important;
        box-shadow: 0 0 0 2px #1abc9c33;
    }
    `;
    document.head.appendChild(style);

    // 监听选区变化
    document.addEventListener('selectionchange', function () {
        // 先移除所有高亮
        document.querySelectorAll('.range-tag.selected').forEach(tag => tag.classList.remove('selected'));
        const sel = window.getSelection();
        if (!sel.rangeCount) return;
        const range = sel.getRangeAt(0);
        // 检查所有.range-tag是否被选中
        document.querySelectorAll('.range-tag').forEach(tag => {
            try {
                if (range.intersectsNode(tag)) {
                    tag.classList.add('selected');
                }
            } catch(e) {}
        });
    });
})();

// 插入.select-anchor样式
(function(){
    const style = document.createElement('style');
    style.innerHTML += `.select-anchor { display:inline-block;width:0;height:1em;vertical-align:middle; }`;
    document.head.appendChild(style);
})();

// 在聊天记录区#messageList添加copy事件监听，支持单标签复制
window.addEventListener('DOMContentLoaded', function () {
    document.getElementById('messageList').addEventListener('copy', function (e) {
        const sel = window.getSelection();
        if (sel.rangeCount === 1) {
            const range = sel.getRangeAt(0);
            // 判断选区是否只包含一个.range-tag
            if (range.startContainer === range.endContainer &&
                range.startContainer.nodeType === 1 &&
                range.startContainer.classList.contains('range-tag')) {
                // 复制标签的HTML
                e.preventDefault();
                const html = range.startContainer.outerHTML;
                e.clipboardData.setData('text/html', html);
                e.clipboardData.setData('text', range.startContainer.innerText);
            }
        }
    });
});

// 在聊天记录区#messageList添加copy事件监听，支持区间组件转文本复制
window.addEventListener('DOMContentLoaded', function () {
    document.getElementById('messageList').addEventListener('copy', function (e) {
        const sel = window.getSelection();
        if (!sel.rangeCount) return;
        const range = sel.getRangeAt(0);
        // 创建一个临时div用于承载复制内容
        const div = document.createElement('div');
        div.appendChild(range.cloneContents());
        // 替换所有.range-tag为@@这是选中的数据范围A2:A13@@文本
        div.querySelectorAll('.range-tag').forEach(tag => {
            const rangeText = tag.getAttribute('data-range') || tag.innerText;
            const textNode = document.createTextNode(`@@这是选中的数据范围${rangeText}@@`);
            tag.parentNode.replaceChild(textNode, tag);
        });
        // 获取纯文本
        const text = div.innerText;
        // 阻止默认复制，设置自定义内容
        e.preventDefault();
        e.clipboardData.setData('text/plain', text);
    });
});

// 右侧已选题号支持拖拽排序
function enableTransferRightDragSort() {
    const list = document.getElementById('transferRightList');
    let dragIdx = null;
    list.ondragstart = function(e) {
        if(e.target.tagName==='LI') {
            dragIdx = Array.from(list.children).indexOf(e.target);
            e.dataTransfer.effectAllowed = 'move';
        }
    };
    list.ondragover = function(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    };
    list.ondrop = function(e) {
        e.preventDefault();
        if(dragIdx===null) return;
        let targetIdx = Array.from(list.children).indexOf(e.target.closest('li'));
        if(targetIdx===-1 || targetIdx===dragIdx) return;
        const moved = window.transferRight.splice(dragIdx,1)[0];
        window.transferRight.splice(targetIdx,0,moved);
        renderTransferLists();
        enableTransferRightDragSort();
        dragIdx = null;
    };
    // 设置draggable属性
    Array.from(list.children).forEach(li=>li.setAttribute('draggable','true'));
}

// ========== 穿梭框多选与拖拽增强 ========== //
function enableTransferMultiSelectAndDrag() {
  // 左侧列表
  const leftList = document.getElementById('transferLeftList');
  const rightList = document.getElementById('transferRightList');
  let lastLeftIndex = null;
  let leftSelected = [];
  let lastRightIndex = null;
  let rightSelected = [];

  function updateActiveClass(list, selected) {
    Array.from(list.children).forEach((li, idx) => {
      if (selected.includes(idx)) {
        li.classList.add('selected');
      } else {
        li.classList.remove('selected');
      }
    });
  }

  // 失去焦点时清空本侧选中
  leftList.onblur = function(e) {
    setTimeout(() => {
      if (document.activeElement !== rightList && !rightList.contains(document.activeElement)) {
        leftSelected = [];
        updateActiveClass(leftList, leftSelected);
      }
    }, 10);
  };
  rightList.onblur = function(e) {
    setTimeout(() => {
      if (document.activeElement !== leftList && !leftList.contains(document.activeElement)) {
        rightSelected = [];
        updateActiveClass(rightList, rightSelected);
      }
    }, 10);
  };
  leftList.tabIndex = 0;
  rightList.tabIndex = 0;

  // 左侧多选
  leftList.onmousedown = function(e) {
    if (e.target.tagName !== 'LI') return;
    const li = e.target;
    const idx = Array.from(leftList.children).indexOf(li);
    if (e.shiftKey && lastLeftIndex !== null) {
      // Shift区间多选
      const [start, end] = [lastLeftIndex, idx].sort((a, b) => a - b);
      leftSelected = [];
      for (let i = start; i <= end; i++) leftSelected.push(i);
    } else if (e.ctrlKey || e.metaKey) {
      // Ctrl不连续多选
      if (leftSelected.includes(idx)) {
        leftSelected = leftSelected.filter(i => i !== idx);
      } else {
        leftSelected.push(idx);
      }
      lastLeftIndex = idx;
    } else {
      // 单选
      leftSelected = [idx];
      lastLeftIndex = idx;
    }
    updateActiveClass(leftList, leftSelected);
  };

  // 右侧多选
  rightList.onmousedown = function(e) {
    if (e.target.tagName !== 'LI') return;
    const li = e.target;
    const idx = Array.from(rightList.children).indexOf(li);
    if (e.shiftKey && lastRightIndex !== null) {
      const [start, end] = [lastRightIndex, idx].sort((a, b) => a - b);
      rightSelected = [];
      for (let i = start; i <= end; i++) rightSelected.push(i);
    } else if (e.ctrlKey || e.metaKey) {
      if (rightSelected.includes(idx)) {
        rightSelected = rightSelected.filter(i => i !== idx);
      } else {
        rightSelected.push(idx);
      }
      lastRightIndex = idx;
    } else {
      rightSelected = [idx];
      lastRightIndex = idx;
    }
    updateActiveClass(rightList, rightSelected);
  };

  // 拖拽左到右
  let dragStartIndices = [];
  leftList.ondragstart = function(e) {
    const li = e.target;
    if (li.tagName !== 'LI') return;
    const idx = Array.from(leftList.children).indexOf(li);
    if (!leftSelected.includes(idx)) {
      leftSelected = [idx];
      lastLeftIndex = idx;
      updateActiveClass(leftList, leftSelected);
    }
    dragStartIndices = [...leftSelected];
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('from', 'left');
    e.dataTransfer.setData('indices', dragStartIndices.join(','));
  };
  rightList.ondragover = function(e) { e.preventDefault(); };
  rightList.ondrop = function(e) {
    e.preventDefault();
    if (e.dataTransfer.getData('from') !== 'left') return;
    const indices = (e.dataTransfer.getData('indices') || '').split(',').map(Number).filter(i => !isNaN(i));
    if (indices.length === 0) return;
    // 保持顺序移动到右侧
    const moving = indices.map(i => window.transferLeft[i]);
    window.transferRight.push(...moving);
    // 从大到小删除，防止错位
    indices.sort((a, b) => b - a).forEach(idx => window.transferLeft.splice(idx, 1));
    renderTransferLists();
    enableTransferMultiSelectAndDrag();
  };
  // 拖拽右到左
  rightList.ondragstart = function(e) {
    const li = e.target;
    if (li.tagName !== 'LI') return;
    const idx = Array.from(rightList.children).indexOf(li);
    if (!rightSelected.includes(idx)) {
      rightSelected = [idx];
      lastRightIndex = idx;
      updateActiveClass(rightList, rightSelected);
    }
    dragStartIndices = [...rightSelected];
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('from', 'right');
    e.dataTransfer.setData('indices', dragStartIndices.join(','));
  };
  leftList.ondragover = function(e) { e.preventDefault(); };
  leftList.ondrop = function(e) {
    e.preventDefault();
    if (e.dataTransfer.getData('from') !== 'right') return;
    const indices = (e.dataTransfer.getData('indices') || '').split(',').map(Number).filter(i => !isNaN(i));
    if (indices.length === 0) return;
    // 保持顺序移动回左侧
    const moving = indices.map(i => window.transferRight[i]);
    window.transferLeft.push(...moving);
    indices.sort((a, b) => b - a).forEach(idx => window.transferRight.splice(idx, 1));
    renderTransferLists();
    enableTransferMultiSelectAndDrag();
  };
  // 重新渲染后重新绑定
  updateActiveClass(leftList, leftSelected);
  updateActiveClass(rightList, rightSelected);
  // 设置draggable属性
  Array.from(leftList.children).forEach(li => li.setAttribute('draggable', 'true'));
  Array.from(rightList.children).forEach(li => li.setAttribute('draggable', 'true'));
}

window.initTransferSortable = function(containerId, data, targetKeys, onChange) {
  // 按numId升序排序data
  data.sort((a, b) => a.numId - b.numId);
  const container = document.getElementById(containerId);
  if (!container) return;
  container.innerHTML = `
    <div class="mb-2">
      <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
        <i class="fas fa-info-circle me-1"></i>
        <strong>操作提示：</strong>拖拽题块来设置相关变量
      </div>
    </div>
    <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
      <div style="flex:1;display:flex;flex-direction:column;">
        <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
          <ul id="left-list"></ul>
        </div>
      </div>
      <div class="transfer-actions" style="display:flex;flex-direction:column;justify-content:center;gap:16px;align-items:center;">
        <button id="to-right" style="width:40px;height:40px;border-radius:50%;font-size:20px;">&gt;</button>
        <button id="to-left" style="width:40px;height:40px;border-radius:50%;font-size:20px;">&lt;</button>
      </div>
      <div style="flex:1;display:flex;flex-direction:column;">
        <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
          <ul id="right-list"></ul>
        </div>
      </div>
    </div>
  `;
  // 渲染时始终按原始顺序（data）过滤
  let leftData = data.filter(q => !targetKeys.includes(q.key));
  let rightData = data.filter(q => targetKeys.includes(q.key));
  let leftSelected = [];
  let rightSelected = [];
  let lastLeftIndex = null;
  let lastRightIndex = null;
  function render() {
    // 渲染前排序
    leftData.sort((a, b) => a.numId - b.numId);
    rightData.sort((a, b) => a.numId - b.numId);
    document.getElementById('left-list').innerHTML = leftData.map(q =>
      `<li data-key="${q.key}">${q.title} <span class="badge" style="cursor:pointer;">${q.type}</span></li>`
    ).join('');
    document.getElementById('right-list').innerHTML = rightData.map(q =>
      `<li data-key="${q.key}">${q.title} <span class="badge" style="cursor:pointer;">${q.type}</span></li>`
    ).join('');
    // 新增：让badge点击等价于li点击
    Array.from(document.getElementById('left-list').children).forEach(li => {
      const badge = li.querySelector('.badge');
      if (badge) {
        badge.onclick = function(e) {
          e.stopPropagation();
          li.click();
        };
      }
    });
    Array.from(document.getElementById('right-list').children).forEach(li => {
      const badge = li.querySelector('.badge');
      if (badge) {
        badge.onclick = function(e) {
          e.stopPropagation();
          li.click();
        };
      }
    });
    updateActiveClass();
    // 新增：右侧为空时显示不同分析类型的提示
    const rightList = document.getElementById('right-list');
    if (rightList && rightList.children.length === 0) {
      // 获取分析类型
      let analysisType = '';
      try {
        analysisType = document.getElementById('analysisType').value;
      } catch {}
      let emptyText = '分析项';
      if (analysisType === 'cronbach' || analysisType === 'factor') {
        emptyText = '分析项（量表）';
      } else if (analysisType === 'correlation') {
        emptyText = '分析项（定量）';
      } else if (analysisType === 'frequency') {
        emptyText = '分析项（定类）';
      } else if (analysisType === 'desc') {
        emptyText = '分析项（定量）';
      } else if (analysisType === 'crossTab') {
        emptyText = '分析项（题目/分组）';
      }
      rightList.setAttribute('data-empty-text', emptyText);
    } 
  }
  function updateActiveClass() {
    Array.from(document.getElementById('left-list').children).forEach((li, idx) => {
      if (leftSelected.includes(idx)) li.classList.add('selected');
      else li.classList.remove('selected');
    });
    Array.from(document.getElementById('right-list').children).forEach((li, idx) => {
      if (rightSelected.includes(idx)) li.classList.add('selected');
      else li.classList.remove('selected');
    });
  }
  render();
  // 多选逻辑
  document.getElementById('left-list').onclick = function(e) {
    if (e.target.tagName !== 'LI') return;
    const lis = Array.from(this.children);
    const idx = lis.indexOf(e.target);
    if (e.shiftKey && lastLeftIndex !== null) {
      const [start, end] = [lastLeftIndex, idx].sort((a, b) => a - b);
      leftSelected = [];
      for (let i = start; i <= end; i++) leftSelected.push(i);
    } else if (e.ctrlKey || e.metaKey) {
      if (leftSelected.includes(idx)) leftSelected = leftSelected.filter(i => i !== idx);
      else leftSelected.push(idx);
      lastLeftIndex = idx;
    } else {
      leftSelected = [idx];
      lastLeftIndex = idx;
    }
    updateActiveClass();
  };
  document.getElementById('right-list').onclick = function(e) {
    if (e.target.tagName !== 'LI') return;
    const lis = Array.from(this.children);
    const idx = lis.indexOf(e.target);
    if (e.shiftKey && lastRightIndex !== null) {
      const [start, end] = [lastRightIndex, idx].sort((a, b) => a - b);
      rightSelected = [];
      for (let i = start; i <= end; i++) rightSelected.push(i);
    } else if (e.ctrlKey || e.metaKey) {
      if (rightSelected.includes(idx)) rightSelected = rightSelected.filter(i => i !== idx);
      else rightSelected.push(idx);
      lastRightIndex = idx;
    } else {
      rightSelected = [idx];
      lastRightIndex = idx;
    }
    updateActiveClass();
  };
  // 按钮批量移动
  document.getElementById('to-right').onclick = function(e) {
    if (e) e.preventDefault();
    if (leftSelected.length === 0) return;
    // 保持原始顺序
    const moving = leftSelected.map(idx => leftData[idx]);
    rightData = rightData.concat(moving);
    leftData = leftData.filter((_, idx) => !leftSelected.includes(idx));
    leftSelected = [];
    render();
    onChange(rightData.map(q=>q.key));
  };
  document.getElementById('to-left').onclick = function(e) {
    if (e) e.preventDefault();
    if (rightSelected.length === 0) return;
    const moving = rightSelected.map(idx => rightData[idx]);
    leftData = leftData.concat(moving);
    rightData = rightData.filter((_, idx) => !rightSelected.includes(idx));
    rightSelected = [];
    render();
    onChange(rightData.map(q=>q.key));
  };
  // 拖拽支持（多选拖动）
  new Sortable(document.getElementById('left-list'), {
    group: 'transfer',
    animation: 150,
    onStart: function(evt) {
      updateActiveClass();
    },
    onEnd: function(evt) {
      leftSelected = [];
      updateActiveClass();
    },
    onMove: function (evt) {
      updateActiveClass();
    },
    onAdd: function (evt) {
      // 拖拽到左侧
      const key = evt.item.getAttribute('data-key');
      // 支持多选拖动
      const movingKeys = rightSelected.length > 0 ? rightSelected.map(idx => rightData[idx].key) : [key];
      movingKeys.forEach(k => {
        const idx = rightData.findIndex(q => q.key === k);
        if (idx !== -1) {
          leftData.push(rightData[idx]);
          rightData.splice(idx, 1);
        }
      });
      rightSelected = [];
      render();
      onChange(rightData.map(q=>q.key));
    }
  });
  new Sortable(document.getElementById('right-list'), {
    group: 'transfer',
    animation: 150,
    onStart: function(evt) {
      updateActiveClass();
    },
    onEnd: function(evt) {
      rightSelected = [];
      updateActiveClass();
    },
    onMove: function (evt) {
      updateActiveClass();
    },
    onAdd: function (evt) {
      // 拖拽到右侧
      const key = evt.item.getAttribute('data-key');
      const movingKeys = leftSelected.length > 0 ? leftSelected.map(idx => leftData[idx].key) : [key];
      movingKeys.forEach(k => {
        const idx = leftData.findIndex(q => q.key === k);
        if (idx !== -1) {
          rightData.push(leftData[idx]);
          leftData.splice(idx, 1);
        }
      });
      leftSelected = [];
      render();
      onChange(rightData.map(q=>q.key));
    }
  });
};

// 新增：线性回归专用穿梭框拖拽
window.initRegressionTransferSortable = function(containerId, data, yKey, xKeys, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    // 题型中文
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    // 初始化左右数据
    let allData = [...data]; // 保留原始顺序
    let yData = [];
    let xData = [];
    function render() {
        // 过滤掉已在右侧的
        const usedKeys = [yData[0]?.key, ...xData.map(i=>i.key)].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const yListHtml = yData.length ?
            `<li data-key="${yData[0].key}">${yData[0].title} <span class="badge">${typeMap[yData[0].type]||yData[0].type}</span></li>` : '';
        const xListHtml = xData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="regression-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">Y(定量)</div>
                    <ul id="regression-right-list-y" data-empty-text="Y(定量)">${yListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:100px;flex:1;">
                    <div class="transfer-list-placeholder">X(定量/定类)</div>
                    <ul id="regression-right-list-x" data-empty-text="X(定量/定类)">${xListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('regression-left-list'), {
            group: {
                name: 'regression',
                put: function(to, from, dragEl) {
                    // 允许从Y区和X区拖回左侧
                    return from.el.id === 'regression-right-list-y' || from.el.id === 'regression-right-list-x';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                // 从右侧拖回左侧，保持原始顺序
                const key = evt.item.getAttribute('data-key');
                // 移除Y区或X区对应项
                if (yData.length && yData[0].key === key) yData = [];
                xData = xData.filter(i=>i.key!==key);
                render();
                onChange(yData[0]?.key, xData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('regression-right-list-y'), {
            group: {
                name: 'regression',
                put: function (to, from, dragEl) {
                    // 允许从左侧或Y/X区域拖入，且只能有一个
                    return (from.el.id === 'regression-left-list' ||
                           from.el.id === 'regression-right-list-y' ||
                           from.el.id === 'regression-right-list-x');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从X区域拖过来的，需要先从X区域移除
                    xData = xData.filter(i => i.key !== key);
                    yData = [item];
                    render();
                    onChange(yData[0]?.key, xData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                yData = [];
                render();
                onChange(null, xData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('regression-right-list-x'), {
            group: {
                name: 'regression',
                put: function (to, from, dragEl) {
                    // 允许从左侧或Y/X区域拖入
                    return (from.el.id === 'regression-left-list' ||
                           from.el.id === 'regression-right-list-x' ||
                           from.el.id === 'regression-right-list-y');
                },
                pull: true
            },
            animation: 150,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !xData.some(i=>i.key===key)) {
                    // 如果是从Y区域拖过来的，需要先从Y区域移除
                    if (yData.length && yData[0].key === key) yData = [];
                    xData.push(item);
                    // 保持原始顺序
                    xData = allData.filter(q => xData.some(i=>i.key===q.key) && (!yData.length || q.key!==yData[0].key));
                    render();
                    onChange(yData[0]?.key, xData.map(i=>i.key));
                }
            },
            onUpdate: function(evt) {
                // 拖拽排序
                const ul = document.getElementById('regression-right-list-x');
                const keys = Array.from(ul.children).map(li => li.getAttribute('data-key'));
                xData = keys.map(k => allData.find(i=>i.key===k)).filter(Boolean);
                onChange(yData[0]?.key, xData.map(i=>i.key));
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                xData = xData.filter(i=>i.key!==key);
                render();
                onChange(yData[0]?.key, xData.map(i=>i.key));
            }
        });
    }
    render();
};

// 新增：方差分析拖拽穿梭框
window.initAnovaTransferSortable = function(containerId, data, xKey, yKeys, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    let allData = [...data];
    let xData = [];
    let yData = [];
    if (xKey) {
        const found = allData.find(q => q.key === xKey);
        if (found) xData = [found];
    }
    if (yKeys && yKeys.length > 0) {
        yData = allData.filter(q => yKeys.includes(q.key));
    }
    function render() {
        const usedKeys = [xData[0]?.key, ...yData.map(i=>i.key)].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const xListHtml = xData.length ?
            `<li data-key="${xData[0].key}">${xData[0].title} <span class="badge">${typeMap[xData[0].type]||xData[0].type}</span></li>` : '';
        const yListHtml = yData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="anova-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">X(定类)</div>
                    <ul id="anova-right-list-x" data-empty-text="X(定类)">${xListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:100px;flex:1;">
                    <div class="transfer-list-placeholder">Y(定量)</div>
                    <ul id="anova-right-list-y" data-empty-text="Y(定量)">${yListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('anova-left-list'), {
            group: {
                name: 'anova',
                put: function(to, from, dragEl) {
                    // 允许从X区和Y区拖回左侧
                    return from.el.id === 'anova-right-list-x' || from.el.id === 'anova-right-list-y';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                const key = evt.item.getAttribute('data-key');
                if (xData.length && xData[0].key === key) xData = [];
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('anova-right-list-x'), {
            group: {
                name: 'anova',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入，且只能有一个
                    return (from.el.id === 'anova-left-list' ||
                           from.el.id === 'anova-right-list-x' ||
                           from.el.id === 'anova-right-list-y');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从Y区域拖过来的，需要先从Y区域移除
                    yData = yData.filter(i => i.key !== key);
                    xData = [item];
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                xData = [];
                render();
                onChange(null, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('anova-right-list-y'), {
            group: {
                name: 'anova',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入
                    return (from.el.id === 'anova-left-list' ||
                           from.el.id === 'anova-right-list-y' ||
                           from.el.id === 'anova-right-list-x');
                },
                pull: true
            },
            animation: 150,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !yData.some(i=>i.key===key)) {
                    // 如果是从X区域拖过来的，需要先从X区域移除
                    if (xData.length && xData[0].key === key) xData = [];
                    yData.push(item);
                    // 保持原始顺序
                    yData = allData.filter(q => yData.some(i=>i.key===q.key) && (!xData.length || q.key!==xData[0].key));
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onUpdate: function(evt) {
                const ul = document.getElementById('anova-right-list-y');
                const keys = Array.from(ul.children).map(li => li.getAttribute('data-key'));
                yData = keys.map(k => allData.find(i=>i.key===k)).filter(Boolean);
                onChange(xData[0]?.key, yData.map(i=>i.key));
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
    }
    render();
};

// 新增：独立T检验拖拽穿梭框
window.initIndependentTTestTransferSortable = function(containerId, data, xKey, yKeys, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    let allData = [...data];
    let xData = [];
    let yData = [];
    if (xKey) {
        const found = allData.find(q => q.key === xKey);
        if (found) xData = [found];
    }
    if (yKeys && yKeys.length > 0) {
        yData = allData.filter(q => yKeys.includes(q.key));
    }
    function render() {
        const usedKeys = [xData[0]?.key, ...yData.map(i=>i.key)].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const xListHtml = xData.length ?
            `<li data-key="${xData[0].key}">${xData[0].title} <span class="badge">${typeMap[xData[0].type]||xData[0].type}</span></li>` : '';
        const yListHtml = yData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="independentTTest-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">X(定类,仅两组)</div>
                    <ul id="independentTTest-right-list-x" data-empty-text="X(定类,仅两组)">${xListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:100px;flex:1;">
                    <div class="transfer-list-placeholder">Y(定量)</div>
                    <ul id="independentTTest-right-list-y" data-empty-text="Y(定量)">${yListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('independentTTest-left-list'), {
            group: {
                name: 'independentTTest',
                put: function(to, from, dragEl) {
                    // 允许从X区和Y区拖回左侧
                    return from.el.id === 'independentTTest-right-list-x' || from.el.id === 'independentTTest-right-list-y';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                const key = evt.item.getAttribute('data-key');
                if (xData.length && xData[0].key === key) xData = [];
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('independentTTest-right-list-x'), {
            group: {
                name: 'independentTTest',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入，且只能有一个
                    return (from.el.id === 'independentTTest-left-list' ||
                           from.el.id === 'independentTTest-right-list-x' ||
                           from.el.id === 'independentTTest-right-list-y');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从Y区域拖过来的，需要先从Y区域移除
                    yData = yData.filter(i => i.key !== key);
                    xData = [item];
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                xData = [];
                render();
                onChange(null, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('independentTTest-right-list-y'), {
            group: {
                name: 'independentTTest',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入
                    return (from.el.id === 'independentTTest-left-list' ||
                           from.el.id === 'independentTTest-right-list-y' ||
                           from.el.id === 'independentTTest-right-list-x');
                },
                pull: true
            },
            animation: 150,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !yData.some(i=>i.key===key)) {
                    // 如果是从X区域拖过来的，需要先从X区域移除
                    if (xData.length && xData[0].key === key) xData = [];
                    yData.push(item);
                    // 保持原始顺序
                    yData = allData.filter(q => yData.some(i=>i.key===q.key) && (!xData.length || q.key!==xData[0].key));
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onUpdate: function(evt) {
                const ul = document.getElementById('independentTTest-right-list-y');
                const keys = Array.from(ul.children).map(li => li.getAttribute('data-key'));
                yData = keys.map(k => allData.find(i=>i.key===k)).filter(Boolean);
                onChange(xData[0]?.key, yData.map(i=>i.key));
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
    }
    render();
};

// 新增：交叉分析专用拖拽穿梭框
window.initCrossTabTransferSortable = function(containerId, data, xKey, yKeys, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    let allData = [...data];
    let xData = [];
    let yData = [];
    if (xKey) {
        const found = allData.find(q => q.key === xKey);
        if (found) xData = [found];
    }
    if (yKeys && yKeys.length > 0) {
        yData = allData.filter(q => yKeys.includes(q.key));
    }
    function render() {
        const usedKeys = [xData[0]?.key, ...yData.map(i=>i.key)].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const xListHtml = xData.length ?
            `<li data-key="${xData[0].key}">${xData[0].title} <span class="badge">${typeMap[xData[0].type]||xData[0].type}</span></li>` : '';
        const yListHtml = yData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="crossTab-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">X(定类)</div>
                    <ul id="crossTab-right-list-x" data-empty-text="X(定类)">${xListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:100px;flex:1;">
                    <div class="transfer-list-placeholder">Y(定类)</div>
                    <ul id="crossTab-right-list-y" data-empty-text="Y(定类)">${yListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('crossTab-left-list'), {
            group: {
                name: 'crossTab',
                put: function(to, from, dragEl) {
                    // 允许从X区和Y区拖回左侧
                    return from.el.id === 'crossTab-right-list-x' || from.el.id === 'crossTab-right-list-y';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                const key = evt.item.getAttribute('data-key');
                if (xData.length && xData[0].key === key) xData = [];
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('crossTab-right-list-x'), {
            group: {
                name: 'crossTab',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入，且只能有一个
                    return (from.el.id === 'crossTab-left-list' ||
                           from.el.id === 'crossTab-right-list-x' ||
                           from.el.id === 'crossTab-right-list-y');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从Y区域拖过来的，需要先从Y区域移除
                    yData = yData.filter(i => i.key !== key);
                    xData = [item];
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                xData = [];
                render();
                onChange(null, yData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('crossTab-right-list-y'), {
            group: {
                name: 'crossTab',
                put: function (to, from, dragEl) {
                    // 允许从左侧或X/Y区域拖入
                    return (from.el.id === 'crossTab-left-list' ||
                           from.el.id === 'crossTab-right-list-y' ||
                           from.el.id === 'crossTab-right-list-x');
                },
                pull: true
            },
            animation: 150,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !yData.some(i=>i.key===key)) {
                    // 如果是从X区域拖过来的，需要先从X区域移除
                    if (xData.length && xData[0].key === key) xData = [];
                    yData.push(item);
                    // 保持原始顺序
                    yData = allData.filter(q => yData.some(i=>i.key===q.key) && (!xData.length || q.key!==xData[0].key));
                    render();
                    onChange(xData[0]?.key, yData.map(i=>i.key));
                }
            },
            onUpdate: function(evt) {
                const ul = document.getElementById('crossTab-right-list-y');
                const keys = Array.from(ul.children).map(li => li.getAttribute('data-key'));
                yData = keys.map(k => allData.find(i=>i.key===k)).filter(Boolean);
                onChange(xData[0]?.key, yData.map(i=>i.key));
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                yData = yData.filter(i=>i.key!==key);
                render();
                onChange(xData[0]?.key, yData.map(i=>i.key));
            }
        });
    }
    render();
};

// 退出登录
function logout() {
    // 重置全局变量
    currentTokenCode = null;
    currentSessionId = null;
    allSessions = [];
    surveyStructureInfo = null;
    surveyStructure = null;
    isShowingTextAnswers = false;
    originalData = null;
    selectedRanges = [];

    // 重置UI显示
    document.getElementById('displayTokenCode').textContent = '未输入';
    document.getElementById('tokenCount').textContent = '0';
    document.getElementById('totalTokenConsumed').textContent = '0';
    document.getElementById('uploadContainer').style.display = 'block';
    document.getElementById('excelTable').style.display = 'none';
    document.getElementById('toolbar').style.display = 'none';
    document.getElementById('exportBtn').style.display = 'none';
    document.getElementById('fullscreenBtn').style.display = 'none';
    document.getElementById('surveyLinkContainer').style.display = 'none';
    document.getElementById('surveyLinkShort').textContent = '';
    document.getElementById('currentSessionTokenConsumed').textContent = '0';
    document.getElementById('sessionList').innerHTML = '';
    document.getElementById('messageList').innerHTML = '';
    document.getElementById('tokenInputSection').style.display = 'block';
    document.getElementById('uploadSection').style.display = 'none';
    document.getElementById('tokenCode').value = '';
    document.getElementById('sessionCostCard').style.display = 'none';

    // 重置搜索框
    const sessionSearch = document.getElementById('sessionSearch');
    if (sessionSearch) {
        sessionSearch.value = '';
    }

    // 重置版本控制
    const versionControl = document.querySelector('.version-control');
    if (versionControl) versionControl.style.display = 'none';
    const versionSelect = document.getElementById('versionSelect');
    if (versionSelect) {
        versionSelect.innerHTML = '<option value="">请先选择会话</option>';
        versionSelect.disabled = true;
    }

    // 重置分析相关UI
    const analysisDynamicParams = document.getElementById('analysisDynamicParams');
    if (analysisDynamicParams) {
        analysisDynamicParams.innerHTML = '<div class="text-danger">请先选择会话</div>';
    }
    const runAnalysisBtn = document.getElementById('runAnalysisBtn');
    if (runAnalysisBtn) {
        runAnalysisBtn.disabled = true;
    }

    // 重置数据调整相关UI
    const adjustmentDynamicParams = document.getElementById('adjustmentDynamicParams');
    if (adjustmentDynamicParams) {
        adjustmentDynamicParams.innerHTML = '<div class="text-danger">请先选择会话</div>';
    }
    const runAdjustmentBtn = document.getElementById('runAdjustmentBtn');
    if (runAdjustmentBtn) {
        runAdjustmentBtn.disabled = true;
    }

    // 重置表格
    if (hot) {
        hot.loadData([]);
        hot.render();
    }

    // 隐藏切换按钮
    hideToggleAnswerButton();

    // 重置搜索输入框的显示状态
    const sessionSearchInput = document.getElementById('sessionSearchInput');
    if (sessionSearchInput) {
        sessionSearchInput.style.display = 'none';
    }

    // 更新退出登录按钮状态
    updateLogoutHeaderBtn();
    updateLogoutToolbarBtn();

    console.log('用户已退出登录，所有状态已重置');
}

// 工具函数：控制所有退出登录按钮显示
function showAllLogoutBtns(show) {
    var btnHeader = document.getElementById('logoutBtnHeader');
    var btnToolbar = document.getElementById('logoutBtn');
    if (btnHeader) btnHeader.style.display = show ? 'inline-block' : 'none';
    if (btnToolbar) btnToolbar.style.display = show ? 'inline-block' : 'none';
}

// 页面初始化时，默认隐藏所有退出登录按钮（未登录时）
window.addEventListener('DOMContentLoaded', function () {
    showAllLogoutBtns(false);
});

// 工具函数：控制header退出登录按钮显示（仅新建对话界面且已登录时显示）
function updateLogoutHeaderBtn() {
    var btnHeader = document.getElementById('logoutBtnHeader');
    var uploadContainer = document.getElementById('uploadContainer');
    // 只有已登录且新建对话界面（上传区可见）时显示
    if (btnHeader) {
        btnHeader.style.display = (currentTokenCode && uploadContainer && uploadContainer.style.display !== 'none') ? 'inline-block' : 'none';
    }
}

// 工具函数：控制toolbar右侧退出登录按钮显示（只要已登录就显示）
function updateLogoutToolbarBtn() {
    var btnToolbar = document.getElementById('logoutBtn');
    if (btnToolbar) btnToolbar.style.display = currentTokenCode ? 'inline-block' : 'none';
}

// 页面初始化时，全部隐藏
window.addEventListener('DOMContentLoaded', function () {
    updateLogoutHeaderBtn();
    updateLogoutToolbarBtn();
});

// 新增：调节效应分析专用拖拽穿梭框
window.initModerationTransferSortable = function(containerId, data, yKey, xKey, zKey, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    let allData = [...data];
    let yData = yKey ? [allData.find(q => q.key === yKey)].filter(Boolean) : [];
    let xData = xKey ? [allData.find(q => q.key === xKey)].filter(Boolean) : [];
    let zData = zKey ? [allData.find(q => q.key === zKey)].filter(Boolean) : [];
    function render() {
        const usedKeys = [yData[0]?.key, xData[0]?.key, zData[0]?.key].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const yListHtml = yData.length ?
            `<li data-key="${yData[0].key}">${yData[0].title} <span class="badge">${typeMap[yData[0].type]||yData[0].type}</span></li>` : '';
        const xListHtml = xData.length ?
            `<li data-key="${xData[0].key}">${xData[0].title} <span class="badge">${typeMap[xData[0].type]||xData[0].type}</span></li>` : '';
        const zListHtml = zData.length ?
            `<li data-key="${zData[0].key}">${zData[0].title} <span class="badge">${typeMap[zData[0].type]||zData[0].type}</span></li>` : '';
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="moderation-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">Y(定量)</div>
                    <ul id="moderation-right-list-y" data-empty-text="Y(定量)">${yListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">X(自变量)</div>
                    <ul id="moderation-right-list-x" data-empty-text="X(自变量)">${xListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">Z(调节变量)</div>
                    <ul id="moderation-right-list-z" data-empty-text="Z(调节变量)">${zListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('moderation-left-list'), {
            group: {
                name: 'moderation',
                put: function(to, from, dragEl) {
                    // 允许从Y/X/Z区拖回左侧
                    return from.el.id === 'moderation-right-list-y' || from.el.id === 'moderation-right-list-x' || from.el.id === 'moderation-right-list-z';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                const key = evt.item.getAttribute('data-key');
                if (yData.length && yData[0].key === key) yData = [];
                if (xData.length && xData[0].key === key) xData = [];
                if (zData.length && zData[0].key === key) zData = [];
                render();
                onChange(yData[0]?.key, xData[0]?.key, zData[0]?.key);
            }
        });
        new Sortable(document.getElementById('moderation-right-list-y'), {
            group: {
                name: 'moderation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或其他Y/X/Z区域拖入，且只能有一个
                    return (from.el.id === 'moderation-left-list' ||
                           from.el.id === 'moderation-right-list-y' ||
                           from.el.id === 'moderation-right-list-x' ||
                           from.el.id === 'moderation-right-list-z');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从其他区域拖过来的，需要先清除原区域的数据
                    if (xData.length && xData[0].key === key) xData = [];
                    if (zData.length && zData[0].key === key) zData = [];
                    yData = [item];
                    render();
                    onChange(yData[0]?.key, xData[0]?.key, zData[0]?.key);
                }
            },
            onRemove: function(evt) {
                yData = [];
                render();
                onChange(null, xData[0]?.key, zData[0]?.key);
            }
        });
        new Sortable(document.getElementById('moderation-right-list-x'), {
            group: {
                name: 'moderation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或其他Y/X/Z区域拖入，且只能有一个
                    return (from.el.id === 'moderation-left-list' ||
                           from.el.id === 'moderation-right-list-y' ||
                           from.el.id === 'moderation-right-list-x' ||
                           from.el.id === 'moderation-right-list-z');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从其他区域拖过来的，需要先清除原区域的数据
                    if (yData.length && yData[0].key === key) yData = [];
                    if (zData.length && zData[0].key === key) zData = [];
                    xData = [item];
                    render();
                    onChange(yData[0]?.key, xData[0]?.key, zData[0]?.key);
                }
            },
            onRemove: function(evt) {
                xData = [];
                render();
                onChange(yData[0]?.key, null, zData[0]?.key);
            }
        });
        new Sortable(document.getElementById('moderation-right-list-z'), {
            group: {
                name: 'moderation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或其他Y/X/Z区域拖入，且只能有一个
                    return (from.el.id === 'moderation-left-list' ||
                           from.el.id === 'moderation-right-list-y' ||
                           from.el.id === 'moderation-right-list-x' ||
                           from.el.id === 'moderation-right-list-z');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从其他区域拖过来的，需要先清除原区域的数据
                    if (yData.length && yData[0].key === key) yData = [];
                    if (xData.length && xData[0].key === key) xData = [];
                    zData = [item];
                    render();
                    onChange(yData[0]?.key, xData[0]?.key, zData[0]?.key);
                }
            },
            onRemove: function(evt) {
                zData = [];
                render();
                onChange(yData[0]?.key, xData[0]?.key, null);
            }
        });
    }
    render();
};

// 新增：中介效应分析专用拖拽穿梭框
window.initMediationTransferSortable = function(containerId, data, yKey, xKeys, mKeys, onChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    const typeMap = {"1":"填空","2":"填空","3":"单选","4":"多选","5":"量表","6single":"矩阵单选","6multiple":"矩阵多选","7":"下拉","8":"滑条","11":"排序"};
    let allData = [...data];
    let yData = yKey ? [allData.find(q => q.key === yKey)].filter(Boolean) : [];
    let xData = xKeys && xKeys.length > 0 ? allData.filter(q => xKeys.includes(q.key)) : [];
    let mData = mKeys && mKeys.length > 0 ? allData.filter(q => mKeys.includes(q.key)) : [];
    function render() {
        const usedKeys = [yData[0]?.key, ...xData.map(i=>i.key), ...mData.map(i=>i.key)].filter(Boolean);
        const leftListData = allData.filter(q => !usedKeys.includes(q.key));
        const leftListHtml = leftListData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const yListHtml = yData.length ?
            `<li data-key="${yData[0].key}">${yData[0].title} <span class="badge">${typeMap[yData[0].type]||yData[0].type}</span></li>` : '';
        const xListHtml = xData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        const mListHtml = mData.map(q =>
            `<li data-key="${q.key}">${q.title} <span class="badge">${typeMap[q.type]||q.type}</span></li>`
        ).join('');
        container.innerHTML = `
        <div class="mb-2">
            <div class="alert alert-info py-2 px-3" style="font-size: 0.875rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle me-1"></i>
                <strong>操作提示：</strong>拖拽题块来设置相关变量
            </div>
        </div>
        <div class="transfer-container" style="display:flex;gap:24px;align-items:stretch;">
            <div style="flex:1;display:flex;flex-direction:column;">
                <div class="transfer-list" style="flex:1 1 0;min-height:180px;">
                    <ul id="mediation-left-list">${leftListHtml}</ul>
                </div>
            </div>
            <div class="transfer-right-area" style="flex:1;display:flex;flex-direction:column;gap:16px;">
                <div class="transfer-list transfer-list-y" style="min-height:40px;max-height:60px;">
                    <div class="transfer-list-placeholder">Y(因变量)</div>
                    <ul id="mediation-right-list-y">${yListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:60px;">
                    <div class="transfer-list-placeholder">X(自变量)</div>
                    <ul id="mediation-right-list-x">${xListHtml}</ul>
                </div>
                <div class="transfer-list transfer-list-x" style="min-height:60px;">
                    <div class="transfer-list-placeholder">M(中介变量)</div>
                    <ul id="mediation-right-list-m">${mListHtml}</ul>
                </div>
            </div>
        </div>`;
        // 拖拽
        new Sortable(document.getElementById('mediation-left-list'), {
            group: {
                name: 'mediation',
                put: function(to, from, dragEl) {
                    // 允许从Y/X/M区拖回左侧
                    return from.el.id === 'mediation-right-list-y' || from.el.id === 'mediation-right-list-x' || from.el.id === 'mediation-right-list-m';
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function(evt) {
                const key = evt.item.getAttribute('data-key');
                if (yData.length && yData[0].key === key) yData = [];
                xData = xData.filter(i=>i.key!==key);
                mData = mData.filter(i=>i.key!==key);
                render();
                onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('mediation-right-list-y'), {
            group: {
                name: 'mediation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或Y/X/M区域拖入，且只能有一个
                    return (from.el.id === 'mediation-left-list' ||
                           from.el.id === 'mediation-right-list-y' ||
                           from.el.id === 'mediation-right-list-x' ||
                           from.el.id === 'mediation-right-list-m');
                },
                pull: true
            },
            animation: 150,
            sort: false,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item) {
                    // 如果是从其他区域拖过来的，需要先从原区域移除
                    xData = xData.filter(i => i.key !== key);
                    mData = mData.filter(i => i.key !== key);
                    yData = [item];
                    render();
                    onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                yData = [];
                render();
                onChange(null, xData.map(i=>i.key), mData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('mediation-right-list-x'), {
            group: {
                name: 'mediation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或Y/X/M区域拖入
                    return (from.el.id === 'mediation-left-list' ||
                           from.el.id === 'mediation-right-list-x' ||
                           from.el.id === 'mediation-right-list-y' ||
                           from.el.id === 'mediation-right-list-m');
                },
                pull: true
            },
            animation: 150,
            sort: true,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !xData.some(i=>i.key===key)) {
                    // 如果是从其他区域拖过来的，需要先从原区域移除
                    if (yData.length && yData[0].key === key) yData = [];
                    mData = mData.filter(i => i.key !== key);
                    xData.push(item);
                    render();
                    onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                xData = xData.filter(i=>i.key!==key);
                render();
                onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
            }
        });
        new Sortable(document.getElementById('mediation-right-list-m'), {
            group: {
                name: 'mediation',
                put: function (to, from, dragEl) {
                    // 允许从左侧或Y/X/M区域拖入
                    return (from.el.id === 'mediation-left-list' ||
                           from.el.id === 'mediation-right-list-m' ||
                           from.el.id === 'mediation-right-list-y' ||
                           from.el.id === 'mediation-right-list-x');
                },
                pull: true
            },
            animation: 150,
            sort: true,
            onAdd: function (evt) {
                const key = evt.item.getAttribute('data-key');
                const item = allData.find(q => q.key === key);
                if (item && !mData.some(i=>i.key===key)) {
                    // 如果是从其他区域拖过来的，需要先从原区域移除
                    if (yData.length && yData[0].key === key) yData = [];
                    xData = xData.filter(i => i.key !== key);
                    mData.push(item);
                    render();
                    onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
                }
            },
            onRemove: function(evt) {
                const key = evt.item.getAttribute('data-key');
                mData = mData.filter(i=>i.key!==key);
                render();
                onChange(yData[0]?.key, xData.map(i=>i.key), mData.map(i=>i.key));
            }
        });
    }
    render();
};

/**
 * 导出配置文本到剪贴板
 * @param {string} configText - 配置文本JSON字符串
 */
function exportConfigToClipboard(configText) {
    try {
        // 尝试格式化JSON以便更好地显示
        let formattedConfig;
        try {
            const configObj = JSON.parse(configText);
            formattedConfig = JSON.stringify(configObj, null, 2);
        } catch (e) {
            // 如果解析失败，使用原始文本
            formattedConfig = configText;
        }

        // 尝试使用现代的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(formattedConfig).then(() => {
                showToast('配置已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制到剪贴板失败:', err);
                fallbackCopyToClipboard(formattedConfig);
            });
        } else {
            // 降级到传统方法
            fallbackCopyToClipboard(formattedConfig);
        }
    } catch (error) {
        console.error('导出配置失败:', error);
        showToast('导出配置失败: ' + error.message, 'error');
    }
}

/**
 * 降级的复制到剪贴板方法
 * @param {string} text - 要复制的文本
 */
function fallbackCopyToClipboard(text) {
    try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // 选择并复制
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showToast('配置已复制到剪贴板', 'success');
        } else {
            // 如果复制失败，显示配置内容在模态框中
            showConfigInModal(text);
        }
    } catch (err) {
        console.error('降级复制方法失败:', err);
        showConfigInModal(text);
    }
}

/**
 * 在模态框中显示配置内容（当复制失败时的备用方案）
 * @param {string} configText - 配置文本
 */
function showConfigInModal(configText) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'configExportModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置导出</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>无法自动复制到剪贴板，请手动复制以下配置内容：</p>
                    <textarea class="form-control" rows="15" readonly style="font-family: monospace; font-size: 12px;">${configText}</textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="selectAllInModal()">全选</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 显示模态框
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * 选择模态框中的所有文本
 */
function selectAllInModal() {
    const textarea = document.querySelector('#configExportModal textarea');
    if (textarea) {
        textarea.select();
        textarea.setSelectionRange(0, 99999); // 对于移动设备
    }
}

/**
 * 显示提示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('success', 'error', 'warning', 'info')
 */
function showToast(message, type = 'info') {
    // 如果页面中有现有的toast容器，使用它；否则创建一个
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // 显示toast
    const bootstrapToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 1500
    });
    bootstrapToast.show();

    // toast隐藏后移除DOM元素
    toast.addEventListener('hidden.bs.toast', () => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });
}

// 文件上传相关函数
function initFileUpload() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('excelFile');
    const fileInfoCard = document.getElementById('fileInfoCard');

    if (!fileUploadArea || !fileInput) return;

    // 点击上传区域触发文件选择
    fileUploadArea.addEventListener('click', function(e) {
        if (e.target !== fileInput) {
            fileInput.click();
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            showFileInfo(file);
        }
    });

    // 拖拽事件
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            // 检查文件类型
            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // 将文件设置到input中
                const dt = new DataTransfer();
                dt.items.add(file);
                fileInput.files = dt.files;
                showFileInfo(file);
            } else {
                showToast('请上传Excel文件（.xlsx或.xls格式）', 'error');
            }
        }
    });
}

function showFileInfo(file) {
    const fileInfoCard = document.getElementById('fileInfoCard');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileType = document.getElementById('fileType');
    const uploadTime = document.getElementById('uploadTime');

    if (!fileInfoCard) return;

    // 显示文件信息
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileType.textContent = getFileTypeText(file.name);
    uploadTime.textContent = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });

    // 显示文件信息卡片
    fileInfoCard.classList.add('show');

    // 隐藏上传区域
    document.getElementById('fileUploadArea').style.display = 'none';
}

function removeFile() {
    const fileInput = document.getElementById('excelFile');
    const fileInfoCard = document.getElementById('fileInfoCard');
    const fileUploadArea = document.getElementById('fileUploadArea');

    // 清空文件输入
    fileInput.value = '';

    // 隐藏文件信息卡片
    fileInfoCard.classList.remove('show');

    // 显示上传区域
    fileUploadArea.style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileTypeText(fileName) {
    if (fileName.endsWith('.xlsx')) {
        return 'Excel 工作簿 (.xlsx)';
    } else if (fileName.endsWith('.xls')) {
        return 'Excel 97-2003 工作簿 (.xls)';
    }
    return 'Excel 文件';
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化文件上传功能
    initFileUpload();

    // 为输入框添加回车键支持
    const tokenInput = document.getElementById('tokenCode');
    if (tokenInput) {
        tokenInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                validateToken();
            }
        });
    }

    const surveyLinkInput = document.getElementById('surveyLink');
    if (surveyLinkInput) {
        surveyLinkInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const excelFile = document.getElementById('excelFile').files[0];
                if (excelFile) {
                    uploadAndParse();
                }
            }
        });
    }
});
