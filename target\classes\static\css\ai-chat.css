/* ai-chat.css，内容来自ai-chat.html的<style>标签 */
body {
  background: linear-gradient(to right, #e0f7fa, #80deea);
  min-height: 100vh;
  margin: 0;
  padding: 20px;
}

.main-container {
  display: flex;
  height: calc(100vh - 40px);
  gap: 20px;
}

/* 左侧会话列表样式 */
.session-panel {
  width: 300px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
}

.session-panel.collapsed {
  width: 50px;
}

/* 当面板收起时 */
.session-panel.collapsed .session-header {
  justify-content: center;
  padding: 15px 0;
}

.session-panel.collapsed .session-header h5,
.session-panel.collapsed .session-header img {
  display: none;
}

.session-panel.collapsed .session-header .toggle-btn {
  position: static;
  margin: 0;
  display: flex;
  justify-content: center;
  width: 100%;
}

.session-panel.collapsed .session-list,
.session-panel.collapsed .new-chat-btn {
  display: none;
}

.session-header {
  position: relative;
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

.session-header .toggle-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #666;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.session-header .toggle-btn:hover {
  color: #333;
}

.new-chat-btn {
  margin: 15px;
  padding: 10px;
  background: #f0f2f5;
  border: none;
  border-radius: 8px;
  color: #1890ff;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.new-chat-btn:hover {
  background: #e6f7ff;
}

.new-chat-btn i {
  margin-right: 8px;
}

.session-panel.collapsed .toggle-btn {
  position: static;
  /* 恢复默认定位 */
  transform: none;
  /* 清除之前的变换 */
  margin-left: auto;
  /* 保持按钮在右侧 */
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.session-item {
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-item:hover {
  background: #f0f2f5;
}

.session-item.active {
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.session-item .session-actions {
  display: none;
}

.session-item:hover .session-actions {
  display: flex;
  gap: 5px;
}

.session-item .session-actions button {
  background: none;
  border: none;
  padding: 2px 5px;
  cursor: pointer;
  color: #666;
}

.session-item .session-actions button:hover {
  color: #1890ff;
}

/* 中间数据展示区域样式 */
.data-panel {
  flex: 1;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.data-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 10px;
}

.data-header-top-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  justify-content: space-between; /* 两端对齐 */
  width: 100%; /* 确保占据整行 */
}

/* 问卷链接简写样式 */
.survey-link-short {
  margin-left: 5px;
  font-size: 0.9em;
  color: #1890ff;
  text-decoration: none;
}

.survey-link-short:hover {
  text-decoration: underline;
}

/* 会话搜索样式 */
.session-search {
  padding: 10px;
  border-bottom: 1px solid #eee;
  display: none;
  /* 默认隐藏 */
}

.session-search input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.excel-container {
  flex: 1;
  overflow: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-container {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 90%;
  max-width: 500px;
  border: 1px solid #e8e9ea;
  transition: all 0.3s ease;
}

.upload-container:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.upload-container.dragover {
  border-color: #1890ff;
  background: #e6f7ff;
}

.hot-table {
  width: 100%;
  height: 100%;
  display: none;
}

/* 右侧聊天区域样式 */
.chat-panel {
  width: 400px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.message {
  margin-bottom: 12px;
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 8px;
  word-break: break-word;
  position: relative;
  font-size: 14px;
  line-height: 1.5;
}

.message.assistant {
  margin-right: auto;
  background: #f5f5f5;
  border-top-left-radius: 0;
  color: #333;
  /* 为外部导出按钮预留空间 */
  margin-right: 40px;
}

/* 数据修改动画样式 */
@keyframes cellChange {
  0% {
      background-color: #fff3cd;
      transform: scale(1);
  }
  50% {
      background-color: #ffeeba;
      transform: scale(1.05);
  }
  100% {
      background-color: transparent;
      transform: scale(1);
  }
}

.scanning {
  animation: cellChange 0.5s ease-out;
}

/* 添加修改历史标记样式 */
.cell-modified {
  position: relative;
}

.cell-modified::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 6px;
  background-color: #1890ff;
  border-radius: 50%;
}

/* 自定义悬浮提示样式 */
.cell-tooltip {
  position: fixed;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 6px;
  padding: 0;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-width: 300px;
}

.tooltip-content {
  padding: 8px 12px;
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 6px;
  color: #fff;
  font-size: 13px;
}

.tooltip-change {
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tooltip-label {
  font-weight: 500;
  color: #ccc;
  min-width: 50px;
}

.tooltip-old-value {
  color: #ff7875;
  background: rgba(255, 120, 117, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.tooltip-new-value {
  color: #73d13d;
  background: rgba(115, 209, 61, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

/* 添加小箭头 */
.cell-tooltip::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

/* 当tooltip显示在下方时的箭头 */
.cell-tooltip.bottom::before {
  top: -12px;
  border-top-color: transparent;
  border-bottom-color: rgba(0, 0, 0, 0.9);
}

.message.user {
  margin-left: auto;
  background: #e6f7ff;
  border-top-right-radius: 0;
  color: #333;
}

.message.system {
  margin: 8px auto;
  background: #fff3cd;
  color: #856404;
  text-align: center;
  max-width: 90%;
  font-size: 13px;
  padding: 6px 10px;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.explanation {
  white-space: pre-wrap;
  line-height: 1.5;
  font-size: 14px;
}

/* 调整头像大小 */
.message::before {
  width: 24px;
  height: 24px;
  font-size: 16px;
}

.message.user::before {
  right: -32px;
}

.message.assistant::before {
  left: -32px;
}

.changes {
  font-size: 0.9em;
  color: #666;
  padding: 5px;
  background: rgba(0,0,0,0.05);
  border-radius: 5px;
}

/* 工具栏样式 */
.toolbar {
  padding: 10px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: none;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.toolbar button {
  height: 36px;
  padding: 0 16px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toolbar button i {
  font-size: 1.1em;
}

.toolbar button.btn-outline-primary {
  border: 1px solid #1890ff;
  color: #1890ff;
  background: transparent;
}

.toolbar button.btn-outline-primary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

.toolbar button.btn-primary {
  background: #1890ff;
  border: none;
  color: white;
}

.toolbar button.btn-primary:hover {
  background: #096dd9;
  transform: translateY(-1px);
}

.toolbar button.btn-sm {
  height: 28px !important;
  padding: 0 8px !important;
  font-size: 13px !important;
  border-radius: 3px !important;
  line-height: 1.2 !important;
}

/* 高亮样式优化 */
.highlight {
  background-color: #fff3cd !important;
  animation: highlight-fade 2s ease-out;
}

@keyframes highlight-fade {
  0% {
      background-color: #ffd700 !important;
  }
  100% {
      background-color: transparent;
  }
}

/* 发送按钮加载状态样式 */
.chat-input .btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
}

.chat-input .btn-primary .spinner-border {
  margin-right: 8px;
}

/* 输入框禁用状态样式 */
.chat-input textarea:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

/* 表格单元格过渡效果 */
.htCore td {
  transition: background-color 0.5s ease;
}

/* 表格单元格高亮效果 */
.htCore td.highlight {
  position: relative;
}

.htCore td.highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 215, 0, 0.2);
  pointer-events: none;
}

/* 代币显示样式 */
.token-display {
  background: #f0f2f5;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: bold;
  color: #1890ff;
  display: inline-flex;
  align-items: center;
  height: 32px;
}

/* 导出按钮样式 */
.export-btn {
  height: 32px;
  padding: 0 15px;
  display: inline-flex;
  align-items: center;
  background: #1890ff; /* 深蓝色背景 */
  color: white; /* 白色文字 */
  border: none;
  font-weight: bold;
}

.export-btn:hover {
  background: #096dd9; /* 更深的蓝色 */
  color: white;
}

/* 上传按钮样式 */
.upload-btn {
  margin-top: 20px;
  padding: 10px 30px;
}

/* 分析结果弹窗样式 */
.analysis-modal .modal-dialog {
  max-width: 800px;
}

.analysis-result {
  max-height: 400px;
  overflow-y: auto;
}

/* 时间分类头部样式 */
.session-group-header {
  font-weight: bold;
  padding: 10px;
  margin-top: 10px;
  margin-bottom: 5px;
  background-color: #e9ecef;
  /* 一个浅灰色背景 */
  border-radius: 5px;
  color: #495057;
  /* 深灰色文字 */
  font-size: 0.9em;
}

/* 全屏预览按钮样式 */
.fullscreen-btn {
  margin-left: 10px;
  padding: 0 10px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  background: #1890ff;
  color: white;
  border: none;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
}

.fullscreen-btn:hover {
  background: #096dd9;
}

/* 全屏模式样式 */
.excel-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.excel-container.fullscreen .hot-table {
  flex: 1;
  height: auto;
}

/* 全屏模式下的控制栏 */
.fullscreen-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  display: none;
}

.excel-container.fullscreen .fullscreen-header {
  display: flex;
}

.fullscreen-title {
  font-size: 1.2em;
  font-weight: 500;
  color: #333;
}

.fullscreen-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.fullscreen-btn {
  margin: 0;
  padding: 8px 16px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #1890ff;
  color: white;
  border: none;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fullscreen-btn:hover {
  background: #096dd9;
  transform: translateY(-1px);
}

.fullscreen-btn i {
  font-size: 1.1em;
}

.fullscreen-btn.secondary {
  background: #f0f2f5;
  color: #666;
}

.fullscreen-btn.secondary:hover {
  background: #e6e8eb;
  color: #333;
}

/* Spinner 样式 */
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: text-bottom;
  border: .25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border .75s linear infinite;
  animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

@-webkit-keyframes spinner-border {
  to { -webkit-transform: rotate(360deg); }
}



.chat-message.highlight {
    background: #ffe082;
}

.excel-container.preview-version {
    box-shadow: 0 0 0 4px #ffe082;
    background: #fffde7;
    position: relative;
}
.msg-time {
    font-size: 11px;
    color: #b0b0b0;
    text-align: right;
    margin-top: 2px;
    margin-bottom: 0;
    margin-right: 4px;
    letter-spacing: 0.5px;
}

.preview-tip {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: #fffbe6;
    color: #333;
    padding: 14px 32px 18px 32px;
    border-radius: 14px;
    font-size: 15px;
    z-index: 9999;
    box-shadow: 0 6px 24px rgba(255,224,130,0.18);
    font-weight: 500;
    border: 1.5px solid #ffe082;
    letter-spacing: 1px;
    max-width: 96vw;
    text-align: center;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}
.preview-tip .restore-tip-btn {
    margin-top: 6px;
    background: #1890ff;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 5px 18px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(24,144,255,0.08);
    transition: background 0.2s, box-shadow 0.2s;
}
.preview-tip .restore-tip-btn:hover {
    background: #40a9ff;
    box-shadow: 0 4px 16px rgba(24,144,255,0.18);
}

.message.assistant .msg-time {
    text-align: left;
    margin-left: 4px;
    margin-right: 0;
}
.message.user .msg-time {
    text-align: right;
    margin-right: 4px;
    margin-left: 0;
}

/* 分析表格样式 */
.analysis-table-container {
    margin: 20px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    width: 100%;
    overflow-x: auto;
}

.analysis-table-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 16px;
    padding-right: 2px;
}
.analysis-table-container-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}
.analysis-table-container-header .table-fullscreen-btn,
.analysis-table-container-header .table-download-btn {
    margin-left: 0;
}
.analysis-table-container-header .table-fullscreen-btn + .table-download-btn {
    margin-left: 8px;
}

.analysis-table-container .table {
    margin-bottom: 0;
    font-size: 14px;
    width: auto;
    white-space: nowrap;
}

.analysis-table-container .table th,
.analysis-table-container .table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    padding: 8px;
    min-width: 80px;
}

.analysis-table-container .table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.analysis-table-container .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02);
}

.analysis-table-container .table-bordered th,
.analysis-table-container .table-bordered td {
    border: 1px solid #dee2e6;
}

/* 表格容器样式 */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 1rem;
    padding-bottom: 6px;
}

/* 表格基本样式 */
.analysis-table {
    border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: 1rem;
    table-layout: fixed;
    width: auto;
}

/* 强制所有表格相关元素不换行 */
.analysis-table,
.analysis-table th,
.analysis-table td {
    white-space: nowrap;
}

.analysis-table th,
.analysis-table td {
    padding: 8px 16px;
    border: 1px solid #ddd;
    text-align: left;
    min-width: 80px;
}

.analysis-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
}

/* 数字列的样式 */
.analysis-table td.number {
    text-align: right;
    font-family: monospace;
}

/* 表格hover效果 */
.analysis-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 表格标题样式 */
.table-title {
    font-size: 1.1em;
    font-weight: bold;
    margin: 1rem 0 0.5rem;
    color: #333;
    padding-left: 2px;
    white-space: nowrap;
}

/* 滚动条样式优化 */
.table-container::-webkit-scrollbar {
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 确保表格内容间距合适 */
.analysis-table td > div,
.analysis-table th > div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 80px;
}

/* 表格中的图片样式 */
.analysis-table img {
    max-height: 100px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

/* 图片放大弹窗样式 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.image-modal img {
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.image-modal .close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

/* 表格hover效果 */
.analysis-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 表格响应式布局 */
@media screen and (max-width: 768px) {
    .table-container {
        margin: 0 -15px;
        padding: 0 15px;
    }
}

/* 表格全屏预览样式 */
.table-fullscreen-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    z-index: 9999;
    padding: 20px;
}

.table-fullscreen-modal.active {
    display: flex;
    flex-direction: column;
}

.table-fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: #fff;
    border-radius: 8px 8px 0 0;
}

.table-fullscreen-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.table-fullscreen-actions {
    display: flex;
    gap: 10px;
}

.table-fullscreen-content {
    flex: 1;
    background: #fff;
    border-radius: 0 0 8px 8px;
    padding: 20px;
    overflow: auto;
}

.table-fullscreen-btn {
    background: none;
    border: none;
    color: #1890ff;
    cursor: pointer;
    padding: 4px 8px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
}

.table-fullscreen-btn:hover {
    color: #40a9ff;
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px;
}

.table-download-btn {
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.table-download-btn:hover {
    background: #40a9ff;
}

/* 全屏模式下的表格样式 */
.table-fullscreen-content .analysis-table-container {
    margin: 0;
    box-shadow: none;
    height: 100%;
}

.table-fullscreen-content .analysis-table {
    width: 100%;
    height: 100%;
}

.table-fullscreen-content .table-container {
    margin: 0;
    height: 100%;
}

.analysis-table-container-header .icon-only {
    background: none;
    border: none;
    color: #1890ff;
    font-size: 16px;
    padding: 2px 4px;
    margin-left: 2px;
    cursor: pointer;
    line-height: 1;
    border-radius: 3px;
    transition: background 0.15s, color 0.15s;
    vertical-align: middle;
}
.analysis-table-container-header .icon-only:hover {
    background: #e6f7ff;
    color: #096dd9;
}
.analysis-table-container-header > div {
    display: flex;
    gap: 2px;
    align-items: center;
}

/* 优化分析参数弹窗为80%宽高，居中显示 */
/* 分析参数弹窗水平垂直居中，内容区高度自适应撑满 */
#analysisParamModal .modal-dialog.modal-fullscreen {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 80vw !important;
  height: 80vh !important;
  max-width: 1200px !important;
  max-height: 90vh !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  z-index: 1055;
}
#analysisParamModal .modal-content {
  height: 100% !important;
  border-radius: 12px !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
}
#analysisParamModal .modal-body {
  flex: 1 1 0 !important;
  height: 100% !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  padding: 24px 32px 12px 32px !important;
}
#analysisParamForm {
  flex: 1 1 0 !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  min-width: 0 !important;
  height: 100% !important;
}
#analysisParamModal .modal-dialog,
#analysisParamModal .modal-content,
#analysisParamModal .modal-body,
#analysisParamForm {
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

#analysisParamForm > div,
.transfer-container {
  flex: 1 1 0;
  display: flex;
  flex-direction: row;
  gap: 24px;
  min-height: 0;
  min-width: 0;
  height: 100%;
}

.transfer-title {
  font-weight: 500;
  color: #888;
  margin-bottom: 8px;
  font-size: 15px;
}
.transfer-tip {
  margin-top: 8px;
  color: #aaa;
  font-size: 13px;
}
/* 保证下拉、输入等组件宽度自适应 */
#analysisParamModal .form-select,
#analysisParamModal .form-control {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Transfer美化样式 */
.transfer-list {
  min-width: 200px;
  min-height: 240px;
  background: #f7f9fa; /* 统一浅灰色 */
  border-radius: 10px;
  border: 1px solid #e5e6eb;
  padding: 12px 0;
  margin: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1 1 0;
  height: 100%;
  background: #f7f9fa;
  overflow: hidden;
  position: relative;
}
.transfer-list ul {
  position: relative;
  min-height: 180px;
  margin: 0;
  padding: 0;
  list-style: none;
  height: 100%;
}

.transfer-list ul::after {
  content: attr(data-empty-text);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 16px;
  height: 100%;
  min-height: 180px;
  width: 100%;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  z-index: 0;
}

.transfer-list li {
  position: relative;
  z-index: 1;
  min-width: 100px;
  min-height: 32px;
  margin: 6px 10px;
  padding: 7px 14px;
  background: #fff;
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
  display: flex;
  align-items: center;
  transition: box-shadow 0.2s, background 0.2s, color 0.2s;
  cursor: pointer;
  user-select: text;
}
.transfer-list li:hover {
  background: #e6f7ff;
  color: #1890ff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.10);
}
.transfer-list li.selected, .transfer-list li.active {
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.18);
  border: 2px solid #096dd9;
}
.transfer-list li .badge {
  margin-left: 8px;
  font-size: 12px;
  background: #f0f2f5;
  color: #888;
  border-radius: 4px;
  padding: 2px 6px;
}
.transfer-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
.transfer-actions button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  background: #fff;
  border: 1.5px solid #1890ff;
  color: #1890ff;
  transition: background 0.2s, color 0.2s, border 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.transfer-actions button:hover {
  background: #e6f7ff;
  color: #096dd9;
  border-color: #096dd9;
}

/* 穿梭框全链路100%高度，彻底撑满弹窗 */
#analysisParamModal .modal-dialog,
#analysisParamModal .modal-content,
#analysisParamModal .modal-body,
#analysisParamForm {
  height: 100% !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}
#analysisParamForm > div,
.transfer-container {
  flex: 1 1 0 !important;
  display: flex !important;
  flex-direction: row !important;
  gap: 24px !important;
  min-height: 0 !important;
  min-width: 0 !important;
  height: 100% !important;
}
.transfer-list {
  flex: 1 1 0 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background: #f7f9fa !important;
  border-radius: 10px !important;
  border: 1px solid #e5e6eb !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03) !important;
  overflow: hidden !important;
  position: relative !important;
  min-width: 200px !important;
  min-height: 240px !important;
}
.transfer-list ul {
  flex: 1 1 0 !important;
  height: 100% !important;
  min-height: 0 !important;
  overflow: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

#analysisDynamicParams {
  flex: 2 1 0;
  min-width: 0;
  min-height: 0;
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: 24px;
}

/* 减小穿梭框内第一个题块距离上边框的距离 */
.transfer-list ul li:first-child {
  margin-top: 2px !important;
}

/* 线性回归专用穿梭框样式 */
.transfer-right-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1 1 0;
}
.transfer-list-y {
  min-height: 38px !important;
  max-height: 44px !important;
  height: auto !important;
  background: #f7f9fa;
  border-radius: 10px;
  border: 1.5px dashed #b2dfdb;
  position: relative;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 0 0 0;
}
.transfer-list-y ul {
  min-height: 32px;
  max-height: 40px;
  padding: 0;
  margin: 0;
  list-style: none;
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  overflow: hidden;
  /* 隐藏滚动条 */
  scrollbar-width: none;
}
.transfer-list-y ul::-webkit-scrollbar {
  display: none;
}
.transfer-list-y li {
  margin: 6px 10px;
  padding: 7px 14px;
  background: #fff;
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
  display: flex;
  align-items: center;
  transition: box-shadow 0.2s, background 0.2s, color 0.2s;
  cursor: pointer;
  user-select: text;
  width: calc(100% - 20px);
  box-sizing: border-box;
}
.transfer-list-x {
  min-height: 120px;
  background: #f7f9fa;
  border-radius: 10px;
  border: 1.5px dashed #b2dfdb;
  position: relative;
}
.transfer-list-placeholder {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 16px;
  pointer-events: none;
  z-index: 0;
}
.transfer-list-y ul,
.transfer-list-x ul {
  position: relative;
  z-index: 1;
  min-height: 40px;
  margin: 0;
  padding: 0;
  list-style: none;
  background: transparent;
}
.transfer-list-y li,
.transfer-list-x li {
  margin: 6px 10px;
  padding: 7px 14px;
  background: #fff;
  border-radius: 6px;
  font-size: 14px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
  display: flex;
  align-items: center;
  transition: box-shadow 0.2s, background 0.2s, color 0.2s;
  cursor: pointer;
  user-select: text;
}
.transfer-list-y li.selected, .transfer-list-x li.selected {
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.18);
  border: 2px solid #096dd9;
}
/* 隐藏左右移动按钮 */
.transfer-actions { display: none !important; }

/* 方差分析(anova)界面拖拽穿梭框样式，直接复用线性回归的.transfer-list-y、.transfer-list-x等样式 */
/* 交叉分析(crossTab)界面拖拽穿梭框样式，直接复用线性回归的.transfer-list-y、.transfer-list-x等样式 */

/* 因子分析因子数量下拉框美化 */
#analysisFactors {
  max-width: 180px;
  display: inline-block;
  margin-left: 4px;
}
#analysisFactors + .form-text {
  margin-top: 2px;
  color: #888;
  font-size: 13px;
}

/* 验证代币和创建会话页面样式 */
#tokenInputSection, #uploadSection {
  animation: fadeInUp 0.6s ease-out;
}

#tokenInputSection h4, #uploadSection h4 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 30px;
  font-size: 24px;
  letter-spacing: -0.5px;
}

#tokenInputSection .form-control, #uploadSection .form-control {
  border: 2px solid #e8e9ea;
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafbfc;
  margin-bottom: 20px;
}

#tokenInputSection .form-control:focus, #uploadSection .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  background: white;
  outline: none;
}

#tokenInputSection .form-control::placeholder, #uploadSection .form-control::placeholder {
  color: #a0a6b1;
  font-weight: 400;
}

/* Excel文件上传区域样式 */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 16px;
  padding: 40px 20px;
  text-align: center;
  background: #fafbfc;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  margin-bottom: 20px;
}

.file-upload-area:hover {
  border-color: #007bff;
  background: #f0f8ff;
  transform: translateY(-2px);
}

.file-upload-area.dragover {
  border-color: #007bff;
  background: #e6f3ff;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.file-upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.file-upload-area:hover .file-upload-icon {
  color: #007bff;
  transform: scale(1.1);
}

.file-upload-text {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.file-upload-hint {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

.file-upload-formats {
  font-size: 12px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* 文件信息显示区域 */
.file-info-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: none;
}

.file-info-card.show {
  display: block;
  animation: fadeInUp 0.4s ease-out;
}

.file-info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.file-info-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-info-title i {
  color: #10b981;
  font-size: 18px;
}

.file-remove-btn {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-remove-btn:hover {
  background: #fecaca;
  transform: scale(1.05);
}

.file-info-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.file-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-info-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

/* 现代化按钮样式 */
.btn-modern {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  letter-spacing: 0.5px;
}

.btn-modern:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.btn-modern:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

/* 加载状态按钮 */
.btn-modern.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn-modern.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 淡入向上动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画 */
.auth-section {
  animation: fadeInUp 0.6s ease-out;
}

.upload-section {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

/* 按钮禁用状态 */
.btn-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功状态动画 */
.btn-modern.success {
  background: #28a745 !important;
}

.btn-modern.success::after {
  content: '✓';
  position: absolute;
  font-size: 18px;
  animation: none;
  border: none;
  width: auto;
  height: auto;
}

/* 错误状态动画 */
.btn-modern.error {
  background: #dc3545 !important;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 全局加载状态样式 */
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(3px);
}

.global-loading-overlay.show {
  display: flex;
}

.global-loading-container {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  text-align: center;
  animation: fadeInScale 0.3s ease-out;
  min-width: 200px;
}

.global-loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.global-loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.global-loading-subtext {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 在文件末尾添加响应式优化 */
@media (max-width: 900px) {
  .data-header-top-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .token-display {
    font-size: 13px;
    min-width: 0;
    width: 100%;
    margin: 2px 0;
    word-break: break-all;
    white-space: normal;
    display: block;
  }
  .toolbar-right button,
  .toolbar-left button {
    font-size: 13px;
    padding: 0 8px;
    height: 28px;
  }
}

/* 优化token-display在所有屏幕下的表现 */
.token-display {
  flex-shrink: 0;
  margin: 4px 0;
  min-width: 180px;
  word-break: break-all;
  white-space: normal;
  display: inline-block;
}

/* 保证本会话消费卡片内的“26 代币”不换行 */
.token-info-card .nowrap {
  white-space: nowrap;
}

/* 顶部三张卡片靠右显示 */
.data-header-top-row > .top-cards-right {
  margin-left: auto;
  display: flex;
  gap: 8px;
}
@media (max-width: 900px) {
  .data-header-top-row > .top-cards-right {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    margin-left: 0;
  }
}

/* 卡片式信息块样式（小尺寸紧凑版） */
.token-info-card {
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 10px 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  min-width: 140px;
}
.token-info-icon {
  font-size: 22px;
  color: #1890ff;
  margin-right: 8px;
  flex-shrink: 0;
}
.token-info-main {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.token-info-title {
  font-size: 13px;
  color: #888;
  font-weight: 500;
  margin-bottom: 1px;
}
.token-info-value {
  font-size: 16px;
  color: #1890ff;
  font-weight: bold;
  line-height: 1.2;
}
.token-info-value-green {
  color: #219653;
}
.token-info-value-black {
  color: #222;
}
.token-info-link {
  font-size: 13px;
  color: #1890ff;
  word-break: break-all;
  background: #f5f7fa;
  border-radius: 5px;
  padding: 2px 6px;
  margin-top: 2px;
  display: inline-block;
  text-decoration: none;
  transition: text-decoration 0.2s;
}
.token-info-link:hover {
  text-decoration: underline;
}
@media (max-width: 900px) {
  .token-info-card {
    flex-direction: column;
    align-items: flex-start;
    padding: 7px 6px;
    min-width: 0;
    width: 100%;
    gap: 4px;
  }
  .token-info-icon {
    font-size: 16px;
    margin-right: 0;
  }
  .token-info-value {
    font-size: 13px;
  }
  .token-info-title {
    font-size: 11px;
  }
  .token-info-link {
    font-size: 11px;
    padding: 1px 4px;
  }
}

/* 顶部信息栏响应式美化 */
.top-info-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 0 0 0;
}
.token-info-group {
  display: flex;
  align-items: center;
  background: #f0f2f5;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 15px;
  margin: 0;
  min-width: 120px;
  white-space: nowrap;
}
.token-label {
  color: #1890ff;
  font-weight: bold;
  margin-right: 4px;
}
.token-value {
  color: #333;
  font-weight: bold;
  word-break: break-all;
}
@media (max-width: 900px) {
  .top-info-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    padding: 4px 0 0 0;
  }
  .token-info-group {
    width: 100%;
    font-size: 13px;
    min-width: 0;
    margin: 0;
    padding: 4px 8px;
    border-radius: 6px;
  }
}

/* toolbar-left卡片横向排列，尽量一行展示 */
.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: stretch;
  flex-wrap: nowrap;
  overflow: auto;
}
.toolbar-left > div {
  display: flex;
  gap: 8px;
  align-items: center;
}
@media (max-width: 700px) {
  .toolbar-left {
    flex-wrap: wrap;
    gap: 8px;
  }
  .toolbar-left > div {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 自定义开关样式 */
.custom-switch-container {
  display: inline-block;
  position: relative;
}

.custom-switch-input {
  display: none;
}

.custom-switch-label {
  display: inline-block;
  width: 64px;
  height: 26px;
  background-color: #6c757d;
  border-radius: 13px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
  overflow: hidden;
}

.custom-switch-input:checked + .custom-switch-label {
  background-color: #007bff;
}

.switch-text-positive,
.switch-text-negative {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 13px;
  font-weight: 500;
  color: white;
  transition: opacity 0.3s ease;
  z-index: 1;
  white-space: nowrap;
}

.switch-text-positive {
  left: 10px;
  opacity: 0;
}

.switch-text-negative {
  right: 8px;
  opacity: 1;
}

.custom-switch-input:checked + .custom-switch-label .switch-text-positive {
  opacity: 1;
}

.custom-switch-input:checked + .custom-switch-label .switch-text-negative {
  opacity: 0;
}

.switch-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.custom-switch-input:checked + .custom-switch-label .switch-slider {
  transform: translateX(38px);
}

/* 题目选择容器样式 */
.question-selector-container {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.question-selector-container::-webkit-scrollbar {
  width: 6px;
}

.question-selector-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.question-selector-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.question-selector-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 导出配置按钮样式 */
/* 基础导出配置按钮样式 */
.export-config-btn {
  position: absolute;
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border: 1px solid rgba(0, 123, 255, 0.2);
  border-radius: 4px;
  width: 24px;
  height: 24px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* system角色：按钮在消息内部右上角 */
.export-config-btn-inside {
  top: 8px;
  right: 8px;
}

/* assistant角色：按钮在聊天框外右上角的右边 */
.export-config-btn-outside {
  top: 8px;
  right: -32px;
}

.export-config-btn:hover {
  background: rgba(0, 123, 255, 0.2);
  color: #0056b3;
  transform: scale(1.1);
  border-color: rgba(0, 123, 255, 0.4);
}

.export-config-btn:active {
  transform: scale(0.95);
  background: rgba(0, 123, 255, 0.2);
}

.export-config-btn i {
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
}

/* 确保消息容器有相对定位以便按钮绝对定位 */
.message.assistant {
  position: relative;
}

/* 调试：确保按钮可见 */
.export-config-btn {
  min-width: 24px !important;
  min-height: 24px !important;
}

/* 确保按钮始终可见 */
.message.assistant .export-config-btn {
  opacity: 1;
  transition: all 0.2s ease;
}

/* 响应式设计 - 在小屏幕上调整按钮大小 */
@media (max-width: 768px) {
  .export-config-btn {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }

  .export-config-btn-inside {
    top: 6px;
    right: 6px;
  }

  .export-config-btn-outside {
    top: 6px;
    right: -28px;
  }

  .export-config-btn i {
    font-size: 12px;
  }

  /* 移动端调整assistant消息的右边距 */
  .message.assistant {
    margin-right: 35px;
  }
}